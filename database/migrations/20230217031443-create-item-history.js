"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_inventory_unit_histories", {
      shipment_inventory_unit_history_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      shipment_inventory_id: {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_inventories",
          key: "shipment_inventory_id",
        },
      },
      shipment_job_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_jobs",
          key: "shipment_job_id",
        },
      },
      qr_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "qr_codes",
          key: "qr_code_id",
        },
      },
      unit_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "unit_lists",
          key: "unit_id",
        },
      },
      storage_unit_id: {
        type: Sequelize.STRING,
        allowNull: false
      },
      item_name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },

      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      }
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_inventory_unit_histories");
  },
};

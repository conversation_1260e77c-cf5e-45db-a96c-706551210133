"use strict";
module.exports = (sequelize, DataTypes) => {
  const shipment_job_document = sequelize.define(
    "shipment_job_document",
    {
      shipment_job_document_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      shipment_job_id: DataTypes.INTEGER,
      pdf: DataTypes.TEXT,
      name: DataTypes.TEXT,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    { createdAt: false, updatedAt: false }
  );
  shipment_job_document.associate = function (models) {
    // associations can be defined here
    shipment_job_document.belongsTo(models.shipment_job, {
      foreignKey: "shipment_job_id",
    });
  };
  return shipment_job_document;
};

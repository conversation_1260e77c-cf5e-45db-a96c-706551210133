const roomModel = require("../../models/Admin/roomModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const { request } = require("../../routes/homeRoutes");
const ExcelJS = require("exceljs");
const fs = require("fs");

exports.checkValidRoomController = async (request, response, next) => {
	try {
		const roomListing = await roomModel.checkValidRoomModel(request.body);
		if (roomListing) {
			next();
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.checkValidRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}

}

exports.getRoomListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomListing = await roomModel.getRoomListingModel(request.query, getUserDetails);
		if (roomListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_RETRIEVED_SUCCESS,
				roomListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.getRoomListingController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomController = async (request, response) => {
	try {
		const { room_name, admin_id, company_id, staff_id } = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomDetails = await roomModel.createRoomModel(room_name, admin_id, company_id, staff_id, getUserDetails);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_ADDED_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomOnTheFlyController = async (request, response, next) => {
	let getUserDetails = await commonModel.getUserDetails(request);
	const { room_id, room_name, add_permanently } = request.body;

	if (room_id === -1 || room_id === "-1") {
		if (room_name === undefined || room_name === null || room_name === "") {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Room name can not be empty!",
				{}
			);
		}

		let roomNameDetails = await roomModel.getRoomViaNameModel(room_name, getUserDetails);
		if (!roomNameDetails) {
			let roomDetails = await roomModel.createRoomModelForAddItem(room_name, getUserDetails, add_permanently);
			if (roomDetails) {
				request.body.room_id = roomDetails.shipment_room_id;
				next();
			}
			else {
				commonFunction.generateResponse(
					response,
					SUCCESS_CODE,
					0,
					ROOM_ADDED_FAILURE,
					{}
				);
			}
		}
		else {
			request.body.room_id = roomNameDetails.shipment_room_id;
			next();
		}
	}
	else {
		next();
	}
};

exports.editRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const { room_name } = request.body;
		const roomDetails = await roomModel.editRoomModel(roomId, room_name);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_EDIT_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.editRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.changeShipmentRoomStatus = async (request, response) => {
	try {
		const { shipment_room_id } = request.body;
		const findOldStatusRoom = await roomModel.findOldStatusRoom(shipment_room_id);
		const roomDetails = await roomModel.changeShipmentRoomStatus(shipment_room_id);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				findOldStatusRoom.status == "Active" ? "Room deactivated successfully" : "Room activated successfully",
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STATUS_CHANGE_FAIL,
				{}
			);
	}
	catch (reason) {
		console.log("exports.changeShipmentRoomStatus -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.removeRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const data = await roomModel.removeRoomModel(roomId);
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.removeRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.isValidRoomController = (request, response, next) => {
	try {
		const room_id = request.params.roomId
			? request.params.roomId
			: request.body.room_id
				? request.body.room_id
				: request.body.shipment_room_id;
		const isValidRoom = roomModel.checkRoomExistenceModel(room_id);
		if (isValidRoom) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	} catch (error) {
		console.log("exports.isValidRoomController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.viewRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const roomDetails = await roomModel.getRoomModel(roomId);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_DETAILS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.viewRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.checkRoomAssignToJob = async (request, response, next) => {
	const room_id = request.params.roomId
		? request.params.roomId
		: request.body.room_id
			? request.body.room_id
			: request.body.shipment_room_id;
	const { count } = await roomModel.checkRoomAssignToJob(room_id);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			0,
			request.body && request.body.isInactive ? "Cannot Inactive this Room as this room is in use inside Shipments!" : "Cannot delete this Room as this room is in use inside Shipments!",
			{}
		);
	} else {
		next();
	}
};


exports.batchRoomListStatusChange = async (request, response) => {
	try {
		let { roomList, isActiveFlag } = request.body;
		if (!isActiveFlag) {
			let roomDetails = await roomModel.checkRoomAssignToItems(roomList);
			roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		}
		await roomModel.batchRoomListStatusChange(roomList, isActiveFlag);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			isActiveFlag ? "Room activated successfully" : "Room deactivated successfully",
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchRoomListStatusChange -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

/**
 * @desc Controller to read Excel file and update ISO codes for shipment rooms
 * @method POST
 */
exports.updateRoomIsoCodesFromExcel = async (request, response) => {
	try {
		// Check if file was uploaded
		if (!request.file) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Excel file is required",
				{}
			);
		}

		const filePath = request.file.path;
		const workbook = new ExcelJS.Workbook();

		// Read the Excel file
		await workbook.xlsx.readFile(filePath);

		// Get the first worksheet
		const worksheet = workbook.getWorksheet(1);
		if (!worksheet) {
			// Clean up uploaded file
			fs.unlinkSync(filePath);
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No worksheet found in Excel file",
				{}
			);
		}

		// Extract room data from Excel
		const excelRoomData = [];
		worksheet.eachRow((row, rowNumber) => {
			// Skip header row (assuming first row is header)
			if (rowNumber > 1) {
				const roomName = row.getCell(4).value; // Assuming room name is in first column
				console.log("roomName: ", roomName);

				const isoCode = row.getCell(2).value;  // Assuming ISO code is in second column
				console.log("isoCode: ", isoCode);

				if (roomName && isoCode) {
					excelRoomData.push({
						name: String(roomName).trim(),
						iso_code: String(isoCode).trim()
					});
				}
			}
		});

		if (excelRoomData.length === 0) {
			// Clean up uploaded file
			fs.unlinkSync(filePath);
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No valid room data found in Excel file",
				{}
			);
		}

		// Step 1: First fetch all rooms from database
		console.log("Step 1: Fetching all rooms from database...");
		const dbRooms = await roomModel.getAllRoomsForIsoUpdate();
		console.log(`Found ${dbRooms.length} rooms in database`);

		// Step 2: Process each Excel row and find matching rooms
		console.log("Step 2: Processing Excel data and matching with database rooms...");
		const matchedRoomIds = [];
		const unmatchedExcelRooms = [];
		let totalUpdatesPerformed = 0;

		// For each Excel room, find ALL matching database rooms and update them
		for (const excelRoom of excelRoomData) {
			console.log(`\nProcessing Excel room: "${excelRoom.name}" with ISO code: "${excelRoom.iso_code}"`);

			// Find ALL database rooms that match this Excel room name (case-insensitive)
			const matchingDbRooms = dbRooms.filter(dbRoom =>
				dbRoom.name.toLowerCase().trim() === excelRoom.name.toLowerCase().trim()
			);
			
			console.log("🚀 ~ matchingDbRooms:", matchingDbRooms)

			if (matchingDbRooms.length > 0) {
				console.log(`Found ${matchingDbRooms.length} matching database room(s) for: "${excelRoom.name}"`);

				// Update ISO code for ALL matching rooms
				for (const dbRoom of matchingDbRooms) {
					try {
						// Update the room's ISO code
						await roomModel.updateRoomIsoCodeByName(dbRoom.name, excelRoom.iso_code);
						matchedRoomIds.push(dbRoom.shipment_room_id);
						totalUpdatesPerformed++;

						console.log(`✅ Updated room ID ${dbRoom.shipment_room_id} ("${dbRoom.name}") with ISO code: "${excelRoom.iso_code}"`);
					} catch (updateError) {
						console.log(`❌ Failed to update room ID ${dbRoom.shipment_room_id}: ${updateError.message}`);
					}
				}
			} else {
				unmatchedExcelRooms.push(excelRoom.name);
				console.log(`❌ No matching database room found for: "${excelRoom.name}"`);
			}
		}

		// Step 3: Update non-matching database rooms with ISO code 999
		console.log("\nStep 3: Updating non-matching database rooms with ISO code '999'...");
		const allDbRoomIds = dbRooms.map(room => room.shipment_room_id);
		const nonMatchingDbRoomIds = allDbRoomIds.filter(id => !matchedRoomIds.includes(id));

		let roomsUpdatedWith999 = 0;
		if (nonMatchingDbRoomIds.length > 0) {
			await roomModel.updateNonMatchingRoomsIsoCode(matchedRoomIds);
			roomsUpdatedWith999 = nonMatchingDbRoomIds.length;
			console.log(`✅ Updated ${roomsUpdatedWith999} non-matching rooms with ISO code '999'`);
		} else {
			console.log("No rooms need to be updated with ISO code '999'");
		}

		// Clean up uploaded file
		fs.unlinkSync(filePath);

		// Summary logging
		console.log("\n=== OPERATION SUMMARY ===");
		console.log(`📊 Total Excel rooms processed: ${excelRoomData.length}`);
		console.log(`📊 Total database rooms: ${dbRooms.length}`);
		console.log(`✅ Rooms updated with Excel ISO codes: ${totalUpdatesPerformed}`);
		console.log(`🔢 Rooms updated with ISO code '999': ${roomsUpdatedWith999}`);
		console.log(`❌ Excel rooms not found in database: ${unmatchedExcelRooms.length}`);
		if (unmatchedExcelRooms.length > 0) {
			console.log(`   Unmatched rooms: ${unmatchedExcelRooms.join(', ')}`);
		}
		console.log("=== OPERATION COMPLETED ===\n");

		// Prepare response data
		const responseData = {
			total_excel_rooms: excelRoomData.length,
			matched_rooms: matchedRoomIds.length,
			updated_with_iso_codes: totalUpdatesPerformed,
			updated_with_999: roomsUpdatedWith999,
			unmatched_excel_rooms: unmatchedExcelRooms,
			total_database_rooms: dbRooms.length
		};

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Room ISO codes updated successfully",
			responseData
		);

	} catch (error) {
		console.log("exports.updateRoomIsoCodesFromExcel -> error: ", error);

		// Clean up uploaded file if it exists
		if (request.file && request.file.path && fs.existsSync(request.file.path)) {
			fs.unlinkSync(request.file.path);
		}

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Error processing Excel file",
			{}
		);
	}
};

exports.batchDeleteRoomList = async (request, response) => {
	try {
		let { roomList } = request.body;
		let roomDetails = await roomModel.checkRoomAssignToItems(roomList);

		roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		await roomModel.batchDeleteRoomListStatus(roomList);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchDeleteRoomList -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

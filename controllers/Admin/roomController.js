const roomModel = require("../../models/Admin/roomModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const { request } = require("../../routes/homeRoutes");
const ExcelJS = require("exceljs");
const fs = require("fs");

exports.checkValidRoomController = async (request, response, next) => {
	try {
		const roomListing = await roomModel.checkValidRoomModel(request.body);
		if (roomListing) {
			next();
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.checkValidRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}

}

exports.getRoomListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomListing = await roomModel.getRoomListingModel(request.query, getUserDetails);
		if (roomListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_RETRIEVED_SUCCESS,
				roomListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.getRoomListingController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomController = async (request, response) => {
	try {
		const { room_name, admin_id, company_id, staff_id } = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomDetails = await roomModel.createRoomModel(room_name, admin_id, company_id, staff_id, getUserDetails);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_ADDED_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomOnTheFlyController = async (request, response, next) => {
	let getUserDetails = await commonModel.getUserDetails(request);
	const { room_id, room_name, add_permanently } = request.body;

	if (room_id === -1 || room_id === "-1") {
		if (room_name === undefined || room_name === null || room_name === "") {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Room name can not be empty!",
				{}
			);
		}

		let roomNameDetails = await roomModel.getRoomViaNameModel(room_name, getUserDetails);
		if (!roomNameDetails) {
			let roomDetails = await roomModel.createRoomModelForAddItem(room_name, getUserDetails, add_permanently);
			if (roomDetails) {
				request.body.room_id = roomDetails.shipment_room_id;
				next();
			}
			else {
				commonFunction.generateResponse(
					response,
					SUCCESS_CODE,
					0,
					ROOM_ADDED_FAILURE,
					{}
				);
			}
		}
		else {
			request.body.room_id = roomNameDetails.shipment_room_id;
			next();
		}
	}
	else {
		next();
	}
};

exports.editRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const { room_name } = request.body;
		const roomDetails = await roomModel.editRoomModel(roomId, room_name);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_EDIT_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.editRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.changeShipmentRoomStatus = async (request, response) => {
	try {
		const { shipment_room_id } = request.body;
		const findOldStatusRoom = await roomModel.findOldStatusRoom(shipment_room_id);
		const roomDetails = await roomModel.changeShipmentRoomStatus(shipment_room_id);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				findOldStatusRoom.status == "Active" ? "Room deactivated successfully" : "Room activated successfully",
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STATUS_CHANGE_FAIL,
				{}
			);
	}
	catch (reason) {
		console.log("exports.changeShipmentRoomStatus -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.removeRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const data = await roomModel.removeRoomModel(roomId);
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.removeRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.isValidRoomController = (request, response, next) => {
	try {
		const room_id = request.params.roomId
			? request.params.roomId
			: request.body.room_id
				? request.body.room_id
				: request.body.shipment_room_id;
		const isValidRoom = roomModel.checkRoomExistenceModel(room_id);
		if (isValidRoom) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	} catch (error) {
		console.log("exports.isValidRoomController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.viewRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const roomDetails = await roomModel.getRoomModel(roomId);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_DETAILS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.viewRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.checkRoomAssignToJob = async (request, response, next) => {
	const room_id = request.params.roomId
		? request.params.roomId
		: request.body.room_id
			? request.body.room_id
			: request.body.shipment_room_id;
	const { count } = await roomModel.checkRoomAssignToJob(room_id);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			0,
			request.body && request.body.isInactive ? "Cannot Inactive this Room as this room is in use inside Shipments!" : "Cannot delete this Room as this room is in use inside Shipments!",
			{}
		);
	} else {
		next();
	}
};


exports.batchRoomListStatusChange = async (request, response) => {
	try {
		let { roomList, isActiveFlag } = request.body;
		if (!isActiveFlag) {
			let roomDetails = await roomModel.checkRoomAssignToItems(roomList);
			roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		}
		await roomModel.batchRoomListStatusChange(roomList, isActiveFlag);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			isActiveFlag ? "Room activated successfully" : "Room deactivated successfully",
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchRoomListStatusChange -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

/**
 * @desc Controller to read Excel file and update ISO codes for shipment rooms
 * @method POST
 */
exports.updateRoomIsoCodesFromExcel = async (request, response) => {
	try {
		// Check if file was uploaded
		if (!request.file) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Excel file is required",
				{}
			);
		}

		const filePath = request.file.path;
		const workbook = new ExcelJS.Workbook();

		// Read the Excel file
		await workbook.xlsx.readFile(filePath);

		// Get the first worksheet
		const worksheet = workbook.getWorksheet(1);
		if (!worksheet) {
			// Clean up uploaded file
			fs.unlinkSync(filePath);
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No worksheet found in Excel file",
				{}
			);
		}

		// Extract room data from Excel
		const excelRoomData = [];
		worksheet.eachRow((row, rowNumber) => {
			// Skip header row (assuming first row is header)
			if (rowNumber > 1) {
				const roomName = row.getCell(1).value; // Assuming room name is in first column
				console.log("roomName: ", roomName);
				
				const isoCode = row.getCell(2).value;  // Assuming ISO code is in second column
				console.log("isoCode: ", isoCode);

				if (roomName && isoCode) {
					excelRoomData.push({
						name: String(roomName).trim(),
						iso_code: String(isoCode).trim()
					});
				}
			}
		});

		// if (excelRoomData.length === 0) {
		// 	// Clean up uploaded file
		// 	fs.unlinkSync(filePath);
		// 	return commonFunction.generateResponse(
		// 		response,
		// 		SUCCESS_CODE,
		// 		0,
		// 		"No valid room data found in Excel file",
		// 		{}
		// 	);
		// }

		// Get all rooms from database
		const dbRooms = await roomModel.getAllRoomsForIsoUpdate();

		// Match rooms and prepare updates
		const matchedRooms = [];
		const unmatchedExcelRooms = [];
		const roomUpdates = [];

		// excelRoomData.forEach(excelRoom => {
		// 	const matchedDbRoom = dbRooms.find(dbRoom =>
		// 		dbRoom.name.toLowerCase().trim() === excelRoom.name.toLowerCase().trim()
		// 	);

		// 	if (matchedDbRoom) {
		// 		matchedRooms.push(matchedDbRoom.shipment_room_id);
		// 		roomUpdates.push({
		// 			room_id: matchedDbRoom.shipment_room_id,
		// 			iso_code: excelRoom.iso_code
		// 		});
		// 	} else {
		// 		unmatchedExcelRooms.push(excelRoom.name);
		// 	}
		// });

		// Update matched rooms with their ISO codes
		// if (roomUpdates.length > 0) {
		// 	await roomModel.batchUpdateRoomIsoCodes(roomUpdates);
		// }

		// Update non-matching database rooms with ISO code 999
		// const allDbRoomIds = dbRooms.map(room => room.shipment_room_id);
		// const nonMatchingDbRoomIds = allDbRoomIds.filter(id => !matchedRooms.includes(id));

		// if (nonMatchingDbRoomIds.length > 0) {
		// 	await roomModel.updateNonMatchingRoomsIsoCode(matchedRooms);
		// }

		// Clean up uploaded file
		// fs.unlinkSync(filePath);

		// Prepare response data
		// const responseData = {
		// 	total_excel_rooms: excelRoomData.length,
		// 	matched_rooms: matchedRooms.length,
		// 	updated_with_iso_codes: roomUpdates.length,
		// 	updated_with_999: nonMatchingDbRoomIds.length,
		// 	unmatched_excel_rooms: unmatchedExcelRooms
		// };

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Room ISO codes updated successfully",
			dbRooms
		);

	} catch (error) {
		console.log("exports.updateRoomIsoCodesFromExcel -> error: ", error);

		// Clean up uploaded file if it exists
		if (request.file && request.file.path && fs.existsSync(request.file.path)) {
			fs.unlinkSync(request.file.path);
		}

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Error processing Excel file",
			{}
		);
	}
};

exports.batchDeleteRoomList = async (request, response) => {
	try {
		let { roomList } = request.body;
		let roomDetails = await roomModel.checkRoomAssignToItems(roomList);

		roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		await roomModel.batchDeleteRoomListStatus(roomList);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchDeleteRoomList -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("tag", {
      tag_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "companies",
          key: "company_id",
        },
      },
      name: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      color: {
        type: Sequelize.STRING(100),
        defaultValue: "#000000",
      },
      tag_for: {
        type: Sequelize.ENUM("CUSTOMER", "SHIPMENT"),
        defaultValue: "SHIPMENT",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("tag");
  },
};

"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("customers", "company_id", {
				type: Sequelize.INTEGER,
				allowNull: true,
				onDelete: "CASCADE",
				onUpdate: "CASCADE",
				references: {
					model: "companies",
					key: "company_id",
				},
			});
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("customers", "company_id");
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_job_scanned = sequelize.define(
		"shipment_inventory_job_scanned",
		{
			scan_status_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_inventory_id: DataTypes.INTEGER,
			current_stage_id: { type: DataTypes.INTEGER },
			local_current_stage_id: { type: DataTypes.INTEGER },

			shipment_job_id: { type: DataTypes.INTEGER, allowNull: false },
			scanned_by_staff: { type: DataTypes.INTEGER, allowNull: true },
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now(),
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now(),
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			freezeTableName: true,
		}
	);

	shipment_inventory_job_scanned.associate = function (models) {
		// associations can be defined here
		shipment_inventory_job_scanned.belongsTo(models.shipment_type_stage, {
			foreignKey: "current_stage_id",
		});

		shipment_inventory_job_scanned.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_current_stage_id",
		});

		shipment_inventory_job_scanned.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});
		shipment_inventory_job_scanned.belongsTo(models.shipment_inventory, {
			as: "inventory_id",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory_job_scanned.belongsTo(models.staff, {
			foreignKey: "scanned_by_staff",
		});
	};
	return shipment_inventory_job_scanned;
};

const itemSuggestionModel = require("../../models/Admin/itemSuggestionModel");
const itemIsoCodeModel = require("../../models/Admin/itemIsoCodeModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");


exports.getItemSuggestionListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const itemSuggestionListing = await itemSuggestionModel.getItemListingModel(request.query, getUserDetails)
		if (itemSuggestionListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_RETRIEVED_SUCCESS,
				itemSuggestionListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.getItemSuggestionListingController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createItemSuggestionController = async (request, response) => {
	try {
		const item = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const itemSuggestionDetails = await itemSuggestionModel.createItemSuggestionModel(item, getUserDetails)
		if (itemSuggestionDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_ADDED_SUCCESS,
				itemSuggestionDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createItemSuggestionController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createItemSuggestionOnTheFlyController = async (
	request,
	response,
	next
) => {
	const item = request.body;
	item.item_volume = parseInt(item.volume);
	item.item_weight = parseInt(item.weight);
	if (item.item_name) {
		const itemDetails = await itemSuggestionModel.getItemViaNameModel(item.item_name)
		let getUserDetails = await commonModel.getUserDetails(request);
		if (!itemDetails) itemSuggestionModel.createItemSuggestionModel(item,getUserDetails);
	}
	next();
};

exports.editItemSuggestionController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const item = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const itemSuggestionDetails = await itemSuggestionModel.editItemModel(itemId, item, getUserDetails)
		if (itemSuggestionDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_EDIT_SUCCESS,
				itemSuggestionDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.editItemSuggestionController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.changeItemStatus = async (request, response) => {
	try {
		const { item_id } = request.body;
		const findOldStatusitemSuggestion = await itemSuggestionModel.findOldStatusitemSuggestion(item_id);
		const itemSuggestionDetails = await itemSuggestionModel.changeItemStatus(item_id)
		if (itemSuggestionDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				findOldStatusitemSuggestion.status == "active" ? "Item deactivated successfully" : "Item activated successfully",
				itemSuggestionDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STATUS_CHANGE_FAIL,
				{}
			);
	}
	catch (reason) {
		console.log("exports.changeItemStatus -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.isValidItemSuggestionController = async (request, response, next) => {
	try {
		const itemId = request.params.itemId
			? request.params.itemId
			: request.body.item_id;

		const isValidItemSuggestion = await itemSuggestionModel.checkItemExistenceModel(itemId);
		if (isValidItemSuggestion) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	} catch (error) {
		console.log("exports.isValidItemSuggestionController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.removeItemController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const data = await itemSuggestionModel.removeItemModel(itemId)
		if (data) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_DELETE_SUCCESS,
				{}
			);
		}
	}
	catch (error) {
		console.log("exports.removeItemController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.viewItemController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const itemDetails = await itemSuggestionModel.getItemModel(itemId)
		if (itemDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_DETAILS,
				itemDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.viewItemController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};




exports.batchItemListStatusChange = async (request, response) => {
	try {
		let { itemList, isActiveFlag } = request.body;
		await itemSuggestionModel.batchItemListStatusChange(itemList, isActiveFlag);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			isActiveFlag ? "Item activated successfully" : "Item deactivated successfully",
			{}
		)
	}
	catch (reason) {
		console.log("exports.batchItemListStatusChange -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}


exports.batchDeleteItemList = async (request, response) => {
	try {
		let { itemList } = request.body;
		await itemSuggestionModel.batchDeleteItemList(itemList);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ITEM_SUG_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchDeleteItemList -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

// Check Item ISO Code Names in Item Suggestion Table Controller
exports.checkItemIsoCodeNamesInItemSuggestionController = async (request, response) => {
	try {
		console.log("🔍 Starting Item ISO Code Names Check in Item Suggestion Table");
		console.log("🔍 ~ request:", request.body);

		// Get all item ISO codes
		console.log("📊 Fetching all item ISO codes...");
		const allItemIsoCodes = await itemIsoCodeModel.getAllItemIsoCodes();

		if (!allItemIsoCodes || allItemIsoCodes.length === 0) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No item ISO codes found in the database",
				{
					total_iso_codes: 0,
					exists_in_suggestion: [],
					not_exists_in_suggestion: [],
					summary: {
						total_checked: 0,
						found_in_suggestion: 0,
						not_found_in_suggestion: 0
					}
				}
			);
		}

		console.log(`📋 Found ${allItemIsoCodes.length} item ISO codes to check`);

		// Get all item suggestions for comparison
		console.log("📊 Fetching all item suggestions...");
		const allItemSuggestions = await itemSuggestionModel.getAllItemSuggestionsForComparison();

		console.log(`📋 Found ${allItemSuggestions.length} item suggestions for comparison`);

		// Create a Set of item suggestion names for faster lookup (case-insensitive)
		const suggestionNamesSet = new Set(
			allItemSuggestions.map(item => item.name ? item.name.toLowerCase().trim() : '')
		);

		console.log("🔍 Checking each item ISO code name against item suggestions...");

		const existsInSuggestion = [];
		const notExistsInSuggestion = [];

		// Check each item ISO code name
		allItemIsoCodes.forEach((isoCode, index) => {
			const isoCodeName = isoCode.name ? isoCode.name.toLowerCase().trim() : '';

			const checkResult = {
				id: isoCode.id,
				name: isoCode.name,
				code: isoCode.code,
				name_trimmed: isoCodeName
			};

			if (isoCodeName && suggestionNamesSet.has(isoCodeName)) {
				// Find the matching suggestion(s)
				const matchingSuggestions = allItemSuggestions.filter(
					suggestion => suggestion.name && suggestion.name.toLowerCase().trim() === isoCodeName
				);

				checkResult.matching_suggestions = matchingSuggestions.map(suggestion => ({
					item_suggestion_id: suggestion.item_suggestion_id,
					name: suggestion.name,
					status: suggestion.status,
					company_id: suggestion.company_id
				}));

				existsInSuggestion.push(checkResult);
				console.log(`✅ ${index + 1}/${allItemIsoCodes.length} - "${isoCode.name}" EXISTS in item_suggestion`);
			} else {
				notExistsInSuggestion.push(checkResult);
				console.log(`❌ ${index + 1}/${allItemIsoCodes.length} - "${isoCode.name}" NOT EXISTS in item_suggestion`);
			}
		});

		// Prepare summary
		const summary = {
			total_checked: allItemIsoCodes.length,
			found_in_suggestion: existsInSuggestion.length,
			not_found_in_suggestion: notExistsInSuggestion.length,
			percentage_found: allItemIsoCodes.length > 0 ?
				((existsInSuggestion.length / allItemIsoCodes.length) * 100).toFixed(2) + '%' : '0%'
		};

		console.log("📊 CHECK SUMMARY:");
		console.log(`   - Total ISO codes checked: ${summary.total_checked}`);
		console.log(`   - Found in item_suggestion: ${summary.found_in_suggestion}`);
		console.log(`   - Not found in item_suggestion: ${summary.not_found_in_suggestion}`);
		console.log(`   - Match percentage: ${summary.percentage_found}`);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Item ISO code names check completed successfully",
			{
				total_iso_codes: allItemIsoCodes.length,
				exists_in_suggestion: existsInSuggestion,
				not_exists_in_suggestion: notExistsInSuggestion,
				summary: summary
			}
		);

	} catch (error) {
		console.error("❌ Error in checkItemIsoCodeNamesInItemSuggestionController:", error);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Internal server error while checking item ISO code names",
			{}
		);
	}
};

// Helper function to normalize names for comparison
function normalizeNameForComparison(name) {
	if (!name) return '';
	return name
		.toLowerCase()
		.trim()
		.replace(/[''`]/g, '') // Remove apostrophes and quotes
		.replace(/[^\w\s]/g, '') // Remove special characters except spaces
		.replace(/\s+/g, ' ') // Replace multiple spaces with single space
		.trim();
}

// Helper function to calculate similarity between two strings
function calculateSimilarity(str1, str2) {
	const normalized1 = normalizeNameForComparison(str1);
	const normalized2 = normalizeNameForComparison(str2);

	// If exactly the same after normalization, return 100%
	if (normalized1 === normalized2) {
		return 100;
	}

	// Calculate Levenshtein distance
	const matrix = [];
	const len1 = normalized1.length;
	const len2 = normalized2.length;

	for (let i = 0; i <= len1; i++) {
		matrix[i] = [i];
	}

	for (let j = 0; j <= len2; j++) {
		matrix[0][j] = j;
	}

	for (let i = 1; i <= len1; i++) {
		for (let j = 1; j <= len2; j++) {
			if (normalized1.charAt(i - 1) === normalized2.charAt(j - 1)) {
				matrix[i][j] = matrix[i - 1][j - 1];
			} else {
				matrix[i][j] = Math.min(
					matrix[i - 1][j - 1] + 1, // substitution
					matrix[i][j - 1] + 1,     // insertion
					matrix[i - 1][j] + 1      // deletion
				);
			}
		}
	}

	const distance = matrix[len1][len2];
	const maxLength = Math.max(len1, len2);
	const similarity = maxLength === 0 ? 100 : ((maxLength - distance) / maxLength) * 100;

	return Math.round(similarity * 100) / 100; // Round to 2 decimal places
}

// Update Item Suggestion Names to Match Item ISO Code Names Controller
exports.updateItemSuggestionNamesToMatchIsoCodesController = async (request, response) => {
	try {
		console.log("🔄 Starting Update Item Suggestion Names to Match ISO Codes Process");
		console.log("🔄 ~ request:", request.body);

		// Get similarity threshold from request (default 85%)
		const similarityThreshold = request.body.similarity_threshold || 85;
		const dryRun = request.body.dry_run !== false; // Default to true (dry run)

		console.log(`🎯 Similarity threshold: ${similarityThreshold}%`);
		console.log(`🧪 Dry run mode: ${dryRun ? 'YES (no actual updates)' : 'NO (will update database)'}`);

		// Get all item ISO codes
		console.log("📊 Fetching all item ISO codes...");
		const allItemIsoCodes = await itemIsoCodeModel.getAllItemIsoCodes();

		if (!allItemIsoCodes || allItemIsoCodes.length === 0) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No item ISO codes found in the database",
				{
					total_iso_codes: 0,
					potential_matches: [],
					updates_performed: [],
					summary: {
						total_checked: 0,
						potential_matches_found: 0,
						updates_performed: 0
					}
				}
			);
		}

		// Get all item suggestions for comparison
		console.log("📊 Fetching all item suggestions...");
		const allItemSuggestions = await itemSuggestionModel.getAllItemSuggestionsForComparison();

		console.log(`📋 Found ${allItemIsoCodes.length} item ISO codes`);
		console.log(`📋 Found ${allItemSuggestions.length} item suggestions`);

		const potentialMatches = [];
		const updatesPerformed = [];

		// Check each item suggestion against all ISO codes for potential matches
		console.log("🔍 Finding potential matches...");

		for (let i = 0; i < allItemSuggestions.length; i++) {
			const suggestion = allItemSuggestions[i];

			if (!suggestion.name) continue;

			let bestMatch = null;
			let bestSimilarity = 0;

			// Find the best matching ISO code for this suggestion
			for (const isoCode of allItemIsoCodes) {
				if (!isoCode.name) continue;

				const similarity = calculateSimilarity(suggestion.name, isoCode.name);

				if (similarity >= similarityThreshold && similarity > bestSimilarity) {
					bestSimilarity = similarity;
					bestMatch = {
						iso_code: isoCode,
						similarity: similarity
					};
				}
			}

			if (bestMatch && suggestion.name !== bestMatch.iso_code.name) {
				const matchInfo = {
					suggestion_id: suggestion.item_suggestion_id,
					current_name: suggestion.name,
					proposed_name: bestMatch.iso_code.name,
					iso_code_id: bestMatch.iso_code.id,
					iso_code: bestMatch.iso_code.code,
					similarity: bestMatch.similarity,
					company_id: suggestion.company_id,
					status: suggestion.status
				};

				potentialMatches.push(matchInfo);

				console.log(`🎯 ${i + 1}/${allItemSuggestions.length} - Found match (${bestMatch.similarity}%): "${suggestion.name}" → "${bestMatch.iso_code.name}"`);

				// Perform update if not in dry run mode
				if (!dryRun) {
					try {
						await itemSuggestionModel.updateItemSuggestionName(
							suggestion.item_suggestion_id,
							bestMatch.iso_code.name
						);

						updatesPerformed.push({
							...matchInfo,
							update_status: 'success'
						});

						console.log(`✅ Updated suggestion ID ${suggestion.item_suggestion_id}: "${suggestion.name}" → "${bestMatch.iso_code.name}"`);
					} catch (updateError) {
						console.error(`❌ Failed to update suggestion ID ${suggestion.item_suggestion_id}:`, updateError.message);
						updatesPerformed.push({
							...matchInfo,
							update_status: 'failed',
							error: updateError.message
						});
					}
				}
			} else {
				console.log(`⏭️ ${i + 1}/${allItemSuggestions.length} - No match found for: "${suggestion.name}"`);
			}
		}

		// Prepare summary
		const summary = {
			total_suggestions_checked: allItemSuggestions.length,
			total_iso_codes_available: allItemIsoCodes.length,
			potential_matches_found: potentialMatches.length,
			updates_performed: updatesPerformed.length,
			successful_updates: updatesPerformed.filter(u => u.update_status === 'success').length,
			failed_updates: updatesPerformed.filter(u => u.update_status === 'failed').length,
			similarity_threshold_used: similarityThreshold,
			dry_run_mode: dryRun
		};

		console.log("📊 UPDATE SUMMARY:");
		console.log(`   - Total suggestions checked: ${summary.total_suggestions_checked}`);
		console.log(`   - Potential matches found: ${summary.potential_matches_found}`);
		console.log(`   - Updates performed: ${summary.updates_performed}`);
		console.log(`   - Successful updates: ${summary.successful_updates}`);
		console.log(`   - Failed updates: ${summary.failed_updates}`);
		console.log(`   - Similarity threshold: ${summary.similarity_threshold_used}%`);
		console.log(`   - Dry run mode: ${summary.dry_run_mode}`);

		const message = dryRun
			? `Found ${potentialMatches.length} potential matches (dry run - no updates performed)`
			: `Successfully updated ${summary.successful_updates} item suggestion names to match ISO codes`;

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			message,
			{
				potential_matches: potentialMatches,
				updates_performed: updatesPerformed,
				summary: summary
			}
		);

	} catch (error) {
		console.error("❌ Error in updateItemSuggestionNamesToMatchIsoCodesController:", error);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Internal server error while updating item suggestion names",
			{}
		);
	}
};
const shipmentModelApi = require("../../models/Api/shipmentModel");
const customerModelApi = require("../../models/Api/customerModel");
const customerModel = require("../../models/Admin/customerModel");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");
const shipmentModel = require("../../models/Admin/shipmentModel");
const companyModel = require("../../models/Admin/companyModel");
const homeModel = require("../../models/APP/homeModel");
const qrCodeModelInstance = require("../../models/Admin/qrCodeModel");
const staffModel = require("../../models/Admin/staffModel");
const commonFunction = require("../../assets/common");
const { sendEmail, readFile, randomString, UploadImageS3 } = require("../../assets/common");
const axios = require("axios");
const QRCode = require("qrcode");
const fs = require("fs");
const AWS = require("aws-sdk");
const s3 = new AWS.S3();

const charsetOptions = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

const {
	generateResponse,
	randomSequencedString,
} = require("../../assets/common");

exports.fetchWarehouseList = async (request, response) => {
	try {
		const getCompanyData = await customerModelApi.getCompanyData(request.body);
		if (getCompanyData) {
			const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getCompanyData.integration_key);
			const fetchCompanyJson = JSON.stringify({
				companyIdTokenMoverInventory: getCompanyData.integration_key,
			});

			const fetchCompanyResponse = await axios.post(
				`${MOVER_STORAGE_API_URL}import/mover-inventory/companies`,
				fetchCompanyJson,
				{
					headers: {
						'Content-Type': 'application/json',
						'accessToken': consumerLoginJson.data.accessToken,
					}
				}
			);

			const responseData = fetchCompanyResponse.data;

			if (responseData) {
				generateResponse(
					response,
					SUCCESS_CODE,
					1,
					"Data retrieved successfully.",
					responseData?.data?.warehouses
				);
			}
		} else {
			generateResponse(
				response,
				SUCCESS_CODE,
				1,
				"Data retrieved successfully.",
				{}
			);
		}
	} catch (error) {
		console.log("exports.fetchWarehouseList -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};


exports.consumerLoginJsonFun = async (request, response, integration_key) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {}); consumerLoginJson
	}
}

exports.addShipmentJson = async (request, response, shipmentData, consumerLoginJson, customerStorageID) => {
	function formatDate(inputDate) {
		const date = new Date(inputDate);
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, '0');
		const day = String(date.getUTCDate()).padStart(2, '0');
		return `${month}-${day}-${year}`;
	}

	const addShipmentJson = JSON.stringify({
		shipmentName: shipmentData.shipment_name,
		customerId: customerStorageID.storage_customer_id,
		warehouseId: request.body.warehouseId,
		contactReference: shipmentData.contact_reference,
		accountReference: shipmentData.account_reference,
		opportunityReference: shipmentData.opportunity_reference,
		estArrival: formatDate(shipmentData.pickup_date),
		estDelivery: formatDate(shipmentData.delivery_date),
		workOrderNotes: [

		],
		workOrderReference: shipmentData.wo_reference,
		externalReference: [

		],
		moveCoord: "",
		source: shipmentData.source,
		volume: shipmentData.estimated_volume,
		weight: shipmentData.estimated_weight,
		estUnits: "",
		originAddress: {
			addressLine1: shipmentData.pickup_address,
			addressLine2: shipmentData.pickup_address2,
			city: shipmentData.pickup_city,
			state: shipmentData.pickup_state,
			zipcode: shipmentData.pickup_zipcode,
			country: shipmentData.pickup_country,

		},
		destinationAddress: {
			addressLine1: shipmentData.delivery_address,
			addressLine2: shipmentData.delivery_address2,
			city: shipmentData.delivery_city,
			state: shipmentData.delivery_state,
			zipcode: shipmentData.delivery_zipcode,
			country: shipmentData.delivery_country,
		},
		importedTags: [],
		moverInventoryShipmentId: shipmentData.shipment_job_id,
		createdFromMoverInventory: true
	});



	try {
		const addShipmentResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, addShipmentJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addShipmentResponse) {
			const shipmentDetail = await shipmentModelApi.shipmentStorageIdUpdate(addShipmentResponse.data.data.id, shipmentData.shipment_job_id);
			return shipmentDetail
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "shipment add Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "shipment add Fail", {});
	}
}

exports.createShipmentController = async (request, response) => {
	try {
		const getCompanyData = await customerModelApi.getCompanyData(request.body)

		let params = {
			job_number: randomSequencedString(6),
			created_by_type: "company",
			created_by_id: request.body.company_id,
			...request.body
		};
		let tagShipment = [];
		const shipmentData = await shipmentModel.addShipmentModel(params);

		await shipmentModel.updateCustomerCountModel(shipmentData.customer_id);
		if (params.tag && params.tag.length > 0) {
			params.tag.map((tag) =>
				tagShipment.push({
					shipment_id: shipmentData.shipment_job_id,
					tag_id: tag,
				})
			);
			await shipmentModel.addUpdateTagToShipmentModel(tagShipment, shipmentData.shipment_job_id);
		}

		const fetchShipmentTypeForShipment = await shipmentModel.fetchShipmentTypeForShipment(request.body);
		const createShipmentTypeForShipment = await shipmentModel.createShipmentTypeForShipment(fetchShipmentTypeForShipment, shipmentData);
		const upShipment = await shipmentModel.upShipment(createShipmentTypeForShipment, shipmentData.shipment_job_id);

		let fetchShipmentTypeStagesForShipment = await shipmentTypeModel.fetchShipmentTypeStagesForShipment(request.body);
		let scanIntoStorageCheck = false;
		fetchShipmentTypeStagesForShipment.forEach(async (newData) => {
			if (newData.assign_storage_units_to_items === 1) {
				scanIntoStorageCheck = true
			}
		});

		if (scanIntoStorageCheck && (request.body.warehouseId !== null && request.body.warehouseId !== undefined && request.body.warehouseId !== "")) {
			const getCustomerStorageID = await customerModelApi.getCustomerStorageID(request.body)
			const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getCompanyData.integration_key);
			const addShipmentJson = await this.addShipmentJson(request, response, shipmentData, consumerLoginJson, getCustomerStorageID)
		}
		let createShipmentTypeStagesForShipment = await shipmentTypeModel.createShipmentTypeStagesForShipment(fetchShipmentTypeStagesForShipment, createShipmentTypeForShipment);
		const upShipmentStageUpdate = await shipmentModel.upShipmentStageUpdate(createShipmentTypeStagesForShipment, shipmentData.shipment_job_id);
		const updatedDetails = { ...shipmentData.toJSON(), scanIntoStorageCheck };
		generateResponse(
			response,
			SUCCESS_CODE,
			1,
			SHIPMENT_ADD_SUCCESS,
			updatedDetails
		);
	} catch (error) {
		console.log("exports.createShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

/**Post /qr_code/:jobId/list listShipmentController Controller */
exports.listShipmentController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		//here we fetch qrCodeList
		const listShipmentData = await shipmentModelApi.listShipmentModel(request.body.company_id, request.query);
		if (listShipmentData)
			generateResponse(
				response,
				SUCCESS_CODE,
				1,
				SHIPMENT_RETRIEVED_SUCCESS,
				listShipmentData
			);
		else
			generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				SHIPMENT_LIST_EMPTY,
				{}
			);
	} catch (error) {
		console.log("exports.listShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


/** isValidShipment Controller */
exports.isValidShipmentController = async (request, response, next) => {
	try {
		const { shipmentId } = request.body
		//here we fetch valid shipment details
		const getShipmentModelInstance = await shipmentModelApi.isValidShipmentIdModel(shipmentId);
		if (getShipmentModelInstance) {
			request.body.model_customer_id = getShipmentModelInstance.customer_id;
			request.body.model_customer_email = getShipmentModelInstance.email;
			next();
		} else
			generateResponse(response, NOT_FOUND_CODE, 0, SHIPMENT_EMPTY, {});

	} catch (error) {
		console.log("exports.isValidShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

/**Post /shipment/:shipmentId/link/ assignWorkerShipment Controller */
exports.assignWorkerShipmentController = async (request, response) => {
	try {
		const { role, staff_id } = request.body;
		const { shipmentId } = request.params;
		//here we assign worker to shipment 
		const modelResponse = await shipmentModel.assignShipmentJobWorker(shipmentId, role, staff_id);
		generateResponse(
			response,
			SUCCESS_CODE,
			1,
			SHIPMENT_UPDATED_SUCCESS,
			modelResponse
		)
	} catch (error) {
		console.log("exports.assignWorkerShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


/**Post /shipment/:shipmentId/unlink/ unlinkWorkerShipment Controller */
exports.unlinkWorkerShipmentController = async (request, response) => {
	try {
		const { staff_worker_id, role } = request.body;
		const { shipmentId } = request.params;
		//here we unlink worker to shipment 
		const modelResponse = await shipmentModel.unlinkShipmentJobWorker(shipmentId, staff_worker_id, role);
		generateResponse(
			response,
			SUCCESS_CODE,
			1,
			SHIPMENT_UPDATED_SUCCESS,
			modelResponse
		)
	} catch (error) {
		console.log("exports.unlinkWorkerShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


/**Post /shipment-details/:shipmentId getShipmentDetails Controller */
exports.getShipmentDetailController = async (request, response) => {
	try {
		const { shipmentId } = request.params;
		const { orderingField, orderingWay } = request.query;
		const obj = JSON.parse(JSON.stringify(request.body));
		const getShipmentModelData = await shipmentModelApi.getShipmentDetailModel(
			obj.company_id,
			shipmentId,
			orderingField,
			orderingWay
		);
		if (getShipmentModelData)
			generateResponse(
				response,
				SUCCESS_CODE,
				1,
				SHIPMENT_RETRIEVED_SUCCESS,
				getShipmentModelData
			);
		else
			generateResponse(response, SERVER_ERROR_CODE, 0, SHIPMENT_EMPTY, {});

	} catch (error) {
		console.log("exports.getShipmentDetailController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


/**Post /shipment/:shipmentId updateShipment Controller */
exports.updateShipmentController = async (request, response) => {
	try {
		const { shipmentId } = request.params;
		const requestBody = request.body;
		let tagShipment = [];
		//newChanges
		const removedShipment = await shipmentModel.updateShipmentModel(shipmentId, requestBody);
		if (removedShipment[0] === 1) {
			generateResponse(
				response,
				SUCCESS_CODE,
				1,
				SHIPMENT_UPDATED_SUCCESS,
				removedShipment
			);
		} else
			generateResponse(
				response,
				SUCCESS_CODE,
				0,
				SHIPMENT_NOT_UPDATED_SUCCESS,
				{}
			);

	} catch (error) {
		console.log("exports.updateShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


exports.checkShipmentAssignToJob = async (request, response, next) => {
	const { count } = await shipmentModel.checkShipmentAssignToJob(
		request.body.shipmentId
	);
	if (count && count > 0) {
		generateResponse(
			response,
			EXPECTATION_FAILED_CODE,
			0,
			"Cannot delete this Shipment as the Shipment has items associated!",
			{}
		);
	} else {
		next();
	}
};

exports.removeShipmentController = async (request, response) => {
	try {
		const { shipmentId } = request.body;
		const { model_customer_id } = request.body;
		const removedShipment = await shipmentModel.deleteShipmentModel(shipmentId);
		const updateCustomerCountModel = await shipmentModel.updateCustomerCountModel(model_customer_id);
		generateResponse(
			response,
			SUCCESS_CODE,
			1,
			SHIPMENT_DELETED_SUCCESS,
			removedShipment
		);
	} catch (error) {
		console.log("exports.removeShipmentController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


exports.downloadPdfController = async (request, response) => {
	try {
		request.connection.setTimeout(7200000);
		const { shipmentId } = request.body;
		const myData = await shipmentModelApi.getShipmentDetailModelForPdf(
			shipmentId,
		);

		const client = require('@jsreport/nodejs-client')(JSREPORT_URL, JSREPORT_ADMIN, JSREPORT_PASSWORD)

		const res = await client.render({
			template: {
				name: "/assets/Main",
				recipe: "chrome-pdf",
				engine: "handlebars",
			},
			data: {
				data: myData
			}
		})

		const bodyBuffer = await res.body();
		const fs = require('fs');
		fs.writeFileSync('some.pdf', bodyBuffer)
		let file = fs.createReadStream('some.pdf');
		let stat = fs.statSync('some.pdf');
		response.setHeader('Content-Length', stat.size);
		response.setHeader('Content-Type', 'application/pdf');
		response.setHeader('Content-Disposition', 'attachment; filename=quote.pdf');
		file.pipe(response);
	} catch (error) {
		console.log("exports.downloadPdfController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
}

exports.validWarehouseIdController = async (request, response, next) => {
	try {
		const { warehouseId } = request.body;
		const validWarehouseId = await shipmentModelApi.validWarehouseIdController(warehouseId);
		if (!validWarehouseId) {
			return generateResponse(
				response,
				EXPECTATION_FAILED_CODE,
				0,
				"Please enter valid warehouse Id",
				{}
			);
		}
		next()
	} catch (error) {
		console.log("exports.validWarehouseIdController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
}

exports.unitTypeList = async (request, response) => {
	try {
		const { warehouseId } = request.body;
		const unitTypeList = await shipmentModelApi.unitTypeList(warehouseId);
		return generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Success",
			unitTypeList
		);
	} catch (error) {
		console.log("exports.unitTypeList -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
}

exports.addCustomerJson = async (request, response, CustomerData, consumerLoginJson, getCompanyData) => {
	const addCustomerJson = JSON.stringify({
		importCustomerId: CustomerData.customer_id,
		firstName: (CustomerData.first_name !== undefined) ? CustomerData.first_name : "",
		lastName: (CustomerData.last_name !== undefined) ? CustomerData.last_name : "",
		address: {
			addressLine1: (CustomerData.address1 !== undefined) ? CustomerData.address1 : "",
			addressLine2: (CustomerData.address2 !== undefined) ? CustomerData.address2 : "",
			city: (CustomerData.city !== undefined) ? CustomerData.city : "",
			state: (CustomerData.state !== undefined) ? CustomerData.state : "",
			zipcode: (CustomerData.zipCode !== undefined) ? CustomerData.zipCode : "",
			country: (CustomerData.country !== undefined) ? CustomerData.country : "",
		},
		email: [
			(CustomerData.email !== undefined) ? CustomerData.email : "",
			(CustomerData.email2 !== undefined) ? CustomerData.email2 : "",
		],
		phoneNumber: [
			(CustomerData.phone !== undefined) ? CustomerData.phone : "",
			(CustomerData.phone2 !== undefined) ? CustomerData.phone2 : "",
			(CustomerData.phone3 !== undefined) ? CustomerData.phone3 : "",
		],
		accountId: (CustomerData.account_id !== undefined) ? CustomerData.account_id : "",
		accountName: (CustomerData.account_name !== undefined) ? CustomerData.account_name : "",
		salesRep: (CustomerData.sales_rep !== undefined) ? CustomerData.sales_rep : "",
		companyId: getCompanyData.key_company.storage_company_id,
		importedTags: [

		],
		moverInventoryCustomerId: CustomerData.customer_id,
		createdFromMoverInventory: true,
	})

	try {
		const addCustomerResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, addCustomerJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addCustomerResponse) {
			let params = {
				customer_id: CustomerData.customer_id,
				customerIdStorage: addCustomerResponse.data.data.id
			};

			const customerDetail = await customerModel.customerStorageIdUpdate(params);
			return customerDetail
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
		}

	} catch (error) {
		console.log("error", error)
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
	}
}

exports.emailExistCheckMoverStorageJsonFun = async (request, response, consumerLoginJson) => {
	const data = JSON.stringify({
		email: [
			(response.email !== undefined) ? response.email : "",
			(response.email2 !== undefined) ? response.email2 : "",
		],
	})
	const resultData = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/check-customer-email-already-exist`,
		data,
		{
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
	return resultData.data.data;
}

const generateAndUploadLabel = async (qrCodeString, jobId) => {
	let qrImageFileName = qrCodeString + `_` + jobId + `.png`;
	let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;
	let serverUploadPath = `${QR_UPLOAD_PATH}/${jobId}/original/${qrImageFileName}`;

	let baseFolderPath = `/tmp/qrCodes/${jobId}`;
	if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`/tmp/qrCodes/${jobId}`, { recursive: true });

	await QRCode.toFile(`${qrFilePath}`, qrCodeString).catch((err) => console.log(err));

	// Moving photos to s3 bucket
	let bucketObject = {
		ACL: "public-read",
		Bucket: Const_AWS_BUCKET,
		Body: fs.createReadStream(qrFilePath),
		Key: `${serverUploadPath}`,
	};

	// uploading and removing files from temp folder
	let result = await UploadImageS3(bucketObject, s3);
	if (result) {
		return serverUploadPath;
	}
};

exports.shipmentAndCustomerController = async (request, response) => {
	try {
		let addCustomerJson = {}, customerDetail = {}
		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);

		if (emailCheckCompany > 0) {
			return response.status(CONFLICT_CODE).json({
				status: 0,
				message: "Email can't be same as company email, so please choose another!. ",
				data: {},
			})
		}

		const emailCount = await customerModelApi.checkReExistingEmailAdd(request.body);
		if (emailCount) {
			response.status(CONFLICT_CODE).json({
				status: 0,
				message: CUSTOMER_EMAIL_EXIST,
				data: {},
			});
		}

		const getCompanyData = await customerModelApi.getCompanyData(request.body)

		if (getCompanyData) {
			const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getCompanyData.integration_key);
			const emailExistCheckMoverStorageJsonFun = await this.emailExistCheckMoverStorageJsonFun(request, response, consumerLoginJson);
			if (emailExistCheckMoverStorageJsonFun) {
				response.status(CONFLICT_CODE).json({
					status: 0,
					message: CUSTOMER_EMAIL_EXIST,
					data: {},
				});
			}

			customerDetail = await customerModel.addCustomer(request.body)
			addCustomerJson = await this.addCustomerJson(request, response, customerDetail, consumerLoginJson, getCompanyData)
		}

		let params = {
			job_number: randomSequencedString(6),
			created_by_type: "company",
			created_by_id: request.body.company_id,
			customer_id: customerDetail.customer_id,
			...request.body
		};

		const shipmentData = await shipmentModel.addShipmentModel(params);

		await shipmentModel.updateCustomerCountModel(shipmentData.customer_id);

		const fetchShipmentTypeForShipment = await shipmentModel.fetchShipmentTypeForShipment(request.body);
		const createShipmentTypeForShipment = await shipmentModel.createShipmentTypeForShipment(fetchShipmentTypeForShipment, shipmentData);
		const upShipment = await shipmentModel.upShipment(createShipmentTypeForShipment, shipmentData.shipment_job_id);

		let fetchShipmentTypeStagesForShipment = await shipmentTypeModel.fetchShipmentTypeStagesForShipment(request.body);
		let scanIntoStorageCheck = false;
		fetchShipmentTypeStagesForShipment.forEach(async (newData) => {
			if (newData.assign_storage_units_to_items === 1) {
				scanIntoStorageCheck = true
			}
		});

		if (scanIntoStorageCheck && (request.body.warehouseId !== null && request.body.warehouseId !== undefined && request.body.warehouseId !== "")) {
			const getCustomerStorageID = await customerModelApi.getCustomerStorageID(shipmentData)
			const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getCompanyData.integration_key);
			const addShipmentJson = await this.addShipmentJson(request, response, shipmentData, consumerLoginJson, getCustomerStorageID)
		}

		let createShipmentTypeStagesForShipment = await shipmentTypeModel.createShipmentTypeStagesForShipment(fetchShipmentTypeStagesForShipment, createShipmentTypeForShipment);
		const upShipmentStageUpdate = await shipmentModel.upShipmentStageUpdate(createShipmentTypeStagesForShipment, shipmentData.shipment_job_id);
		const updatedDetails = { ...shipmentData.toJSON(), scanIntoStorageCheck };

		if (request.body.is_customer_portal_invite) {
			let html = await commonFunction.readFile("customerPortal.html");

			const customerDetails = await customerModel.findCustomer(customerDetail.customer_id)

			if (customerDetails.email) {
				html = html.replace(
					/{{customer_name}}/g,
					`${customerDetails.first_name ? customerDetails.first_name : ""} ${customerDetails.last_name ? customerDetails.last_name : ""
					}`
				);
				html = html.replace(
					/{{company_name}}/g,
					`${customerDetails && customerDetails.customer_company
						? customerDetails.customer_company.company_name
						: ""
					}`
				);
				html = html.replace(
					/{{company_email}}/g,
					`${customerDetails && customerDetails.customer_company
						? customerDetails.customer_company.email
						: ""
					}`
				);
				html = html.replace(
					/{{company_phone}}/g,
					`${customerDetails && customerDetails.customer_company
						? customerDetails.customer_company.phone
						: ""
					}`
				);
				html = html.replace(/{{CUSTOMER_PORTAL_LINK}}/g, `${CUSTOMER_PORTAL_LINK}?email=${customerDetails.email}`);
				//newChanges
				const returnResponse = await sendEmail(customerDetails.email, "Your customer portal link", html);
			}
		}

		if (request.body.is_create_label) {
			request.setTimeout(0);
			let qrCodeBulk = [];
			let { label_number } = await qrCodeModelInstance.findLastLabel(shipmentData.shipment_job_id);

			for (let qrCodeString = 0; qrCodeString < request.body.item_count; qrCodeString++) {
				let qrString = randomString(10, charsetOptions);
				let qr_number = label_number + qrCodeString + 1;
				const qrImageFileName = await generateAndUploadLabel(qrString, shipmentData.shipment_job_id);
				if (qrImageFileName) {
					qrCodeBulk.push({
						random_number: qrString,
						qr_image: qrImageFileName,
						job_id: shipmentData.shipment_job_id,
						label_number: qr_number,
					});
				}
			}
			const modelResponse = await qrCodeModelInstance.generateQrModel(qrCodeBulk);
		}

		if (request.body.assign_worker.length > 0) {
			request.body.assign_worker.forEach(async (worker) => {
				const { role, staff_id } = worker;
				const workerExist = await staffModel.isStaffModel(staff_id)
				const stage_id = createShipmentTypeStagesForShipment[0].local_shipment_stage_id
				

				if (workerExist) {

					let modelResponse;

					const isStaffAlreadyAssigned = await shipmentModel.isStaffAlreadyAssignToStage(shipmentData.shipment_job_id, staff_id, stage_id);
					if (isStaffAlreadyAssigned && isStaffAlreadyAssigned.role === role) {
						return generateResponse(response, EXPECTATION_FAILED_CODE, 0, "Worker already assigned to stage. Please try with another", {});
					}

					if (role === "supervisor") {
						const supervisorAssignment = await shipmentModel.isSupervisorAssignToShipmentStage(shipmentData.shipment_job_id, stage_id, role);
						if (supervisorAssignment) {
							await shipmentModel.deleteExitingWorkerFromStage(supervisorAssignment);
						}
					}

					if (isStaffAlreadyAssigned) {
						await shipmentModel.deleteExitingWorkerFromStage(isStaffAlreadyAssigned);
					}

					modelResponse = await shipmentModel.assignNewStaffToShipment(shipmentData.shipment_job_id, role, staff_id, stage_id);
				}
			});
		}

		return generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Success",
			{}
		);

	} catch (error) {
		console.log("exports.shipmentAndCustomerController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}

}
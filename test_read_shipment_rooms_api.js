const axios = require('axios');

async function testReadShipmentRoomsAPI() {
    try {
        console.log("🚀 Testing Read Shipment Rooms Excel API");
        console.log("=========================================");
        
        // API endpoint
        const apiUrl = 'http://localhost:8421/api/admin/room/read-shipment-rooms-excel';
        
        console.log("📡 Making API call to:", apiUrl);
        console.log("📋 Expected behavior:");
        console.log("   1. Read shipment_rooms.xlsx file from docs folder");
        console.log("   2. Extract headers and data from Excel file");
        console.log("   3. Return structured data with file info and content");
        console.log("\n⏳ Processing...\n");
        
        // Make API call
        const response = await axios.get(apiUrl, {
            timeout: 30000 // 30 seconds timeout
        });
        
        console.log("✅ API Response Status:", response.status);
        
        if (response.data.status === 1) {
            console.log("🎉 API call successful!");
            console.log("📊 EXCEL FILE INFORMATION:");
            console.log("=========================================");
            
            const data = response.data.data;
            
            // File info
            console.log("📁 File Information:");
            console.log(`   - File Path: ${data.file_info.file_path}`);
            console.log(`   - File Size: ${data.file_info.file_size_bytes} bytes`);
            console.log(`   - Worksheet Name: ${data.file_info.worksheet_name}`);
            console.log(`   - Total Rows: ${data.file_info.total_rows}`);
            console.log(`   - Total Columns: ${data.file_info.total_columns}`);
            console.log(`   - Data Rows: ${data.file_info.data_rows}`);
            
            // Headers
            console.log("\n📋 Column Headers:");
            if (data.headers && data.headers.length > 0) {
                data.headers.forEach((header, index) => {
                    console.log(`   ${index + 1}. Column ${header.column}: "${header.name}"`);
                });
            } else {
                console.log("   No headers found");
            }
            
            // Sample data
            console.log("\n🔍 Sample Data (First 5 rows):");
            if (data.summary.first_few_rows && data.summary.first_few_rows.length > 0) {
                data.summary.first_few_rows.forEach((row, index) => {
                    console.log(`\n   Row ${row.row_number}:`);
                    Object.keys(row).forEach(key => {
                        if (key !== 'row_number' && row[key] !== null) {
                            console.log(`     ${key}: "${row[key]}"`);
                        }
                    });
                });
            } else {
                console.log("   No data rows found");
            }
            
            // Summary
            console.log("\n📊 Summary:");
            console.log(`   - Total data rows extracted: ${data.summary.total_data_rows}`);
            console.log(`   - Columns with headers: ${data.summary.columns_with_headers}`);
            
            // Show all data if requested
            if (process.argv.includes('--show-all-data')) {
                console.log("\n📄 ALL DATA:");
                console.log("=============");
                console.log(JSON.stringify(data.data, null, 2));
            } else {
                console.log("\n💡 Tip: Add '--show-all-data' flag to see all Excel data");
            }
            
            console.log("\n✅ SUCCESS: Shipment rooms Excel file read successfully!");
            
        } else {
            console.log("⚠️ API returned error:", response.data.message);
            console.log("📄 Full response:", JSON.stringify(response.data, null, 2));
        }
        
    } catch (error) {
        console.log("\n❌ ERROR OCCURRED:");
        console.log("===============================================");
        
        if (error.response) {
            console.log("📡 API Error Response:");
            console.log("Status:", error.response.status);
            console.log("Message:", error.response.data?.message || 'No message');
            console.log("Full Response:", JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log("🌐 Network Error - No response received");
            console.log("Make sure the server is running on port 8421");
            console.log("Command to start server: npm start or node server.js");
        } else {
            console.log("⚠️ Error:", error.message);
        }
    }
}

// Instructions for testing
console.log("📋 TESTING INSTRUCTIONS:");
console.log("========================");
console.log("1. Make sure your server is running: npm start");
console.log("2. Ensure the Excel file exists in docs/shipment_rooms.xlsx");
console.log("3. Authentication is disabled for testing");
console.log("4. Check server console for detailed processing logs");
console.log("5. This test will show the structure and content of the Excel file\n");

// Run the test
testReadShipmentRoomsAPI();

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testRoomIsoAPI() {
    try {
        console.log("🚀 Testing Room ISO Code Update API");
        
        // Check if Excel file exists
        const excelPath = path.join(__dirname, 'docs', 'shipment_rooms.xlsx');
        if (!fs.existsSync(excelPath)) {
            console.log("❌ Excel file not found at:", excelPath);
            return;
        }
        
        console.log("✅ Excel file found at:", excelPath);
        
        // Create form data
        const formData = new FormData();
        formData.append('excel_file', fs.createReadStream(excelPath));
        
        // API endpoint (adjust port if needed)
        const apiUrl = 'http://localhost:8421/api/admin/room/update-iso-codes-from-excel';
        
        console.log("📡 Making API call to:", apiUrl);
        
        // Make API call (without authentication for testing)
        const response = await axios.post(apiUrl, formData, {
            headers: {
                ...formData.getHeaders(),
                // Add authentication header if needed
                // 'Authorization': 'Bearer your_token_here'
            },
            timeout: 30000 // 30 seconds timeout
        });
        
        console.log("✅ API Response Status:", response.status);
        console.log("📊 Response Data:", JSON.stringify(response.data, null, 2));
        
        if (response.data.status === 1) {
            console.log("🎉 API call successful!");
            const data = response.data.data;
            console.log(`📈 Statistics:`);
            console.log(`   - Total Excel rooms: ${data.total_excel_rooms}`);
            console.log(`   - Matched rooms: ${data.matched_rooms}`);
            console.log(`   - Updated with ISO codes: ${data.updated_with_iso_codes}`);
            console.log(`   - Updated with 999: ${data.updated_with_999}`);
            console.log(`   - Unmatched Excel rooms: ${data.unmatched_excel_rooms?.length || 0}`);
        } else {
            console.log("⚠️ API returned error:", response.data.message);
        }
        
    } catch (error) {
        if (error.response) {
            console.log("❌ API Error Response:");
            console.log("Status:", error.response.status);
            console.log("Data:", JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log("❌ Network Error - No response received");
            console.log("Make sure the server is running on port 8421");
        } else {
            console.log("❌ Error:", error.message);
        }
    }
}

// Test without authentication first
async function testWithoutAuth() {
    console.log("=== Testing without authentication ===");
    await testRoomIsoAPI();
}

// Run the test
testWithoutAuth();

'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn(
      'shipment_jobs',
      'company_id',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: 'cascade',
        references:{
          model: 'companies',
          key: "company_id",
        }
      }
    );
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('shipment_jobs', 'company_id');
  }
};

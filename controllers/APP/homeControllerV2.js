const homeModelV2 = require("../../models/APP/homeModelV2");
const commonFunction = require("../../assets/common");
const bcrypt = require("bcrypt");
const AWS = require("aws-sdk");
const fs = require("fs");
const sharp = require('sharp');
const { info } = require("console");
const path = require("path");
const axios = require("axios");
const FormData = require('form-data');
const { findIndex } = require('lodash');


exports.homeControllerV2 = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let headerData = await commonFunction.jwtTokenDecode(request.headers.access_token);
		let homeData = {};

		const [jobList, jobListAssignWorker, jobSummary] = await Promise.all([
			homeModelV2.getUpdatedJobList(request.body, request.query, headerData.payload.user_id),
			homeModelV2.jobListAssignWorker(request.body, request.query, headerData.payload.user_id),
			homeModelV2.jobSummary(request.body, request.query, headerData.payload.user_id)
		]);

		homeData["totalPages"] = Math.ceil(jobList.count / (request.body.page_size ? parseInt(request.body.page_size) : 25));
		homeData["totalShipments"] = jobList.count;
		homeData["jobList"] = jobList.rows;
		homeData["jobListAssignWorker"] = jobListAssignWorker
		homeData["jobSummary"] = jobSummary.rows

		if (jobList !== "" && jobListAssignWorker !== "") {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: JOB_RETRIEVED_SUCCESS,
				data: homeData,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: JOB_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		response.status(EXPECTATION_FAILED_CODE).json({
			status: 0,
			message: JOB_NOT_FOUND,
			data: {},
		});
	}
};

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "admins",
        "roles",
        {
          type: Sequelize.STRING,
          defaultValue: "SUPERADMIN",
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("admins", "roles");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

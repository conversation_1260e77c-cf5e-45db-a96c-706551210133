"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'is_item_assign_to_unit_completed',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "is_item_assign_to_unit_completed");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
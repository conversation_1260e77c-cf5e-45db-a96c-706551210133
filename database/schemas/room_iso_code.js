"use strict";
module.exports = (sequelize, DataTypes) => {
	const room_iso_code = sequelize.define(
		"room_iso_code",
		{
			id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			code: DataTypes.STRING,
			created_at: {
				type: DataTypes.DATE,
			},
			updated_at: {
				type: DataTypes.DATE,
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	room_iso_code.associate = function (models) {
	};
	return room_iso_code;
};

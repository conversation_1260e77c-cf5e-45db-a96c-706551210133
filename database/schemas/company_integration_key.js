"use strict";
module.exports = (sequelize, DataTypes) => {
  const company_integration_key = sequelize.define(
    "company_integration_key",
    {
      company_integration_key_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: DataTypes.INTEGER,
      integration_key: DataTypes.STRING,
      company_identity: DataTypes.STRING,
      status: {
        type: DataTypes.ENUM("inactive", "active"),
        defaultValue: "inactive",
      },
      created_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
      updated_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
    },
    { createdAt: false, updatedAt: false }
  );
  company_integration_key.associate = function (models) {
    // associations can be defined here
    company_integration_key.belongsTo(models.company, {
      as: "key_company",
      foreignKey: "company_id",
    });

  };
  return company_integration_key;
};

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.changeColumn(
        "shipment_inventory_comments",
        "createdAt",
        {
          type: Sequelize.DATE,
          defaultValue: new Date()
        }
      );

      await queryInterface.changeColumn(
        "shipment_inventory_comments",
        "updatedAt",
        {
          type: Sequelize.DATE,
          defaultValue: new Date()
        }
      );

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.changeColumn(
        "shipment_inventory_comments",
        "createdAt",
        {
          type: Sequelize.DATE,
          defaultValue: new Date()
        }
      );

      await queryInterface.changeColumn(
        "shipment_inventory_comments",
        "updatedAt",
        {
          type: Sequelize.DATE,
          defaultValue: new Date()
        }
      );

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

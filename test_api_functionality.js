const ExcelJS = require("exceljs");
const path = require("path");
const roomModel = require("./models/Admin/roomModel");

async function testExcelReading() {
    try {
        console.log("=== Testing Excel File Reading ===");
        
        const filePath = path.join(__dirname, "docs", "shipment_rooms.xlsx");
        console.log("Reading Excel file from:", filePath);
        
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            console.log("❌ No worksheet found");
            return;
        }
        
        console.log("✅ Worksheet found:", worksheet.name);
        console.log("📊 Row count:", worksheet.rowCount);
        console.log("📊 Column count:", worksheet.columnCount);
        
        // Extract room data from Excel (same logic as in controller)
        const excelRoomData = [];
        worksheet.eachRow((row, rowNumber) => {
            // Skip header row (assuming first row is header)
            if (rowNumber > 1) {
                const roomName = row.getCell(1).value; // Room name in first column
                const isoCode = row.getCell(2).value;  // ISO code in second column
                
                if (roomName && isoCode) {
                    excelRoomData.push({
                        name: String(roomName).trim(),
                        iso_code: String(isoCode).trim()
                    });
                }
            }
        });
        
        console.log(`✅ Found ${excelRoomData.length} valid room entries in Excel`);
        
        // Show first few entries
        console.log("\n📋 Sample Excel data:");
        excelRoomData.slice(0, 10).forEach((room, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${room.name.padEnd(25)} -> ${room.iso_code}`);
        });
        
        return excelRoomData;
        
    } catch (error) {
        console.error("❌ Error reading Excel file:", error.message);
        return [];
    }
}

async function testDatabaseConnection() {
    try {
        console.log("\n=== Testing Database Connection ===");
        
        // Test getting all rooms for ISO update
        const dbRooms = await roomModel.getAllRoomsForIsoUpdate();
        console.log(`✅ Found ${dbRooms.length} active rooms in database`);
        
        // Show first few database rooms
        console.log("\n📋 Sample database rooms:");
        dbRooms.slice(0, 10).forEach((room, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ID:${room.shipment_room_id} | ${room.name.padEnd(25)} | ISO: ${room.iso_code || 'NULL'}`);
        });
        
        return dbRooms;
        
    } catch (error) {
        console.error("❌ Error connecting to database:", error.message);
        return [];
    }
}

async function testMatching(excelRooms, dbRooms) {
    try {
        console.log("\n=== Testing Room Matching Logic ===");
        
        const matchedRooms = [];
        const unmatchedExcelRooms = [];
        const roomUpdates = [];

        excelRooms.forEach(excelRoom => {
            const matchedDbRoom = dbRooms.find(dbRoom => 
                dbRoom.name.toLowerCase().trim() === excelRoom.name.toLowerCase().trim()
            );
            
            if (matchedDbRoom) {
                matchedRooms.push(matchedDbRoom.shipment_room_id);
                roomUpdates.push({
                    room_id: matchedDbRoom.shipment_room_id,
                    room_name: matchedDbRoom.name,
                    old_iso_code: matchedDbRoom.iso_code,
                    new_iso_code: excelRoom.iso_code
                });
            } else {
                unmatchedExcelRooms.push(excelRoom.name);
            }
        });

        console.log(`✅ Matched rooms: ${matchedRooms.length}`);
        console.log(`⚠️  Unmatched Excel rooms: ${unmatchedExcelRooms.length}`);
        
        // Show matched rooms that will be updated
        console.log("\n📋 Rooms to be updated:");
        roomUpdates.slice(0, 10).forEach((update, index) => {
            console.log(`${(index + 1).toString().padStart(2)}. ${update.room_name.padEnd(25)} | ${(update.old_iso_code || 'NULL').padEnd(8)} -> ${update.new_iso_code}`);
        });
        
        if (unmatchedExcelRooms.length > 0) {
            console.log("\n⚠️  Excel rooms not found in database:");
            unmatchedExcelRooms.slice(0, 10).forEach((roomName, index) => {
                console.log(`${(index + 1).toString().padStart(2)}. ${roomName}`);
            });
        }
        
        // Calculate rooms that will get ISO code 999
        const allDbRoomIds = dbRooms.map(room => room.shipment_room_id);
        const nonMatchingDbRoomIds = allDbRoomIds.filter(id => !matchedRooms.includes(id));
        
        console.log(`\n🔢 Database rooms that will get ISO code '999': ${nonMatchingDbRoomIds.length}`);
        
        return {
            matchedRooms: matchedRooms.length,
            roomUpdates: roomUpdates.length,
            nonMatchingRooms: nonMatchingDbRoomIds.length,
            unmatchedExcelRooms: unmatchedExcelRooms.length
        };
        
    } catch (error) {
        console.error("❌ Error in matching logic:", error.message);
        return null;
    }
}

async function runTests() {
    console.log("🚀 Starting API Functionality Tests\n");
    
    // Test Excel reading
    const excelRooms = await testExcelReading();
    if (excelRooms.length === 0) {
        console.log("❌ Cannot proceed without Excel data");
        return;
    }
    
    // Test database connection
    const dbRooms = await testDatabaseConnection();
    if (dbRooms.length === 0) {
        console.log("❌ Cannot proceed without database data");
        return;
    }
    
    // Test matching logic
    const results = await testMatching(excelRooms, dbRooms);
    if (results) {
        console.log("\n=== Test Summary ===");
        console.log(`📊 Total Excel rooms: ${excelRooms.length}`);
        console.log(`📊 Total database rooms: ${dbRooms.length}`);
        console.log(`✅ Rooms to be updated with Excel ISO codes: ${results.roomUpdates}`);
        console.log(`🔢 Rooms to be set to ISO code '999': ${results.nonMatchingRooms}`);
        console.log(`⚠️  Excel rooms not found in database: ${results.unmatchedExcelRooms}`);
        console.log("\n✅ All tests completed successfully!");
    }
}

// Run the tests
runTests().catch(console.error);

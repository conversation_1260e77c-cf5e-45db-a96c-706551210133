require("dotenv").config();

module.exports = {
  development: {
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.MYSQL_DB,
    host: process.env.DB_HOST,
    dialect: "mysql",
    dialectOptions: { decimalNumbers: true },
    pool: {
      max: 30,
      min: 0,
      acquire: 1000000,
      idle: 10000,
    },
  },
  test: {
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.MYSQL_DB,
    host: process.env.DB_HOST,
    dialect: "mysql",
    dialectOptions: { decimalNumbers: true },
    pool: {
      max: 30,
      min: 0,
      acquire: 1000000,
      idle: 10000,
    },
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.MYSQL_DB,
    host: process.env.DB_HOST,
    dialect: "mysql",
    dialectOptions: { decimalNumbers: true },
    pool: {
      max: 30,
      min: 0,
      acquire: 1000000,
      idle: 10000,
    },
  },
  master: {
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.MYSQL_DB,
    host: process.env.DB_HOST,
    dialect: "mysql",
    dialectOptions: { decimalNumbers: true },
    pool: {
      max: 30,
      min: 0,
      acquire: 1000000,
      idle: 10000,
    },
  },
};

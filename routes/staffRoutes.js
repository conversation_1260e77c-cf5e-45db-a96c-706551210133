const express = require("express");
const router = express();
const staffController = require("../controllers/APP/staffController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const { errorHandler } = require("../assets/common");
const multer = require("multer");
const upload = multer({ dest: "temp/" });

/**
* @desc Api for staff signIn.
* @method POST
*/

router
  .route("/sign-in")
  .post(
    upload.none(),
    validators.app.staffSignIn,
    errorHandler,
    staffController.signIn
  );

/**
* @desc Api for staff signIn with companyId.
* @method POST
*/

router
  .route("/sign-in/with-companyId")
  .post(
    upload.none(),
    validators.app.signInWithcompanyId,
    errorHandler,
    staffController.signInWithcompanyId
  );

/**
* @desc Api for staff logout.
* @method POST
*/

router
  .route("/logout")
  .post(
    authentication.validateToken,
    upload.none(),
    staffController.logout
  );

/**
* @desc Api for forgot password.
* @method POST
*/

router
  .route("/forgot-password")
  .post(
    upload.none(),
    validators.app.staffForgotPassword,
    errorHandler,
    staffController.forgotPassword
  );

/**
* @desc Api for staff change password.
* @method POST
*/

router
  .route("/change-password")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.staffChangePassword,
    errorHandler,
    staffController.changePassword
  );

/**
* @desc Api for staff change password storage.
* @method POST
*/

router
  .route("/change-password-storage")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.staffChangePasswordStorage,
    errorHandler,
    staffController.changePasswordStorage
  );

/**
* @desc Api for fetch staff profile.
* @method POST
*/

router
  .route("/staff-profile")
  .post(
    authentication.validateToken,
    upload.none(),
    staffController.getStaffProfile
  );

module.exports = router;

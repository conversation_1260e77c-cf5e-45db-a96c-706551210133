const {
	qr_code,
	qr_setting,
	company,
	shipment_job,
	tag,
	shipment_inventory: inventory,
	sequelize,
} = require("../../database/schemas");
const { Op } = require("sequelize");

exports.getQrIdByRandomNumber = async (randomNumber) => {
	return qr_code.findOne({
		where: {
			random_number: randomNumber
		}
	})
}

exports.checksingalQrCodeExistence = async (qrCode) => {
	return qr_code.findOne({
		where: {
			qr_code_id: qrCode
		}
	})
}

exports.generateQrForExternal = async (jobId, randomNumber, labelNumber) => {
	const qrCodeObj = {
		job_id: jobId,
		random_number: randomNumber,
		type: "External",
		qr_image: null,
		label_number: labelNumber + 1,
		company_id: null
	}
	return qr_code.create(qrCodeObj);
}

exports.qrTypeChecking = async (QrId) => {
	return qr_code.findOne({
		where: {
			qr_code_id: QrId
		},
	})
}

exports.CheckExternalRandomNumber = async (request, RandomNumber) => {
	let data = await qr_code.findOne({
		where: {
			random_number: RandomNumber
		},
		attributes: [
			"qr_code_id",
			"job_id",
			"company_id",
			"batch_id",
			"label_number",
			"random_number",
			"qr_image",
			"status",
			"type",
			"created_at",
			"updated_at",
			[
				sequelize.literal(
					`(SELECT item_name FROM shipment_inventories WHERE qr_code.qr_code_id = shipment_inventories.qr_id LIMIT 1)`
				),
				"item_name",
			],
		],
		raw: true,
	});

	if (data !== null) {
		if (data.item_name !== null) {
			return true
		}
		request.body.type = data.type
		request.body.qr_id = data.qr_code_id
		return false
	}
	else {
		return false
	}

}

exports.genericQrUpdate = async (QrId, JobId) => {
	return qr_code.update(
		{
			job_id: JobId
		},
		{ where: { qr_code_id: QrId } }
	);

}

exports.itemTagList = async (companyId) => {
	const tagList = await tag.findAll({
		where:
		{
			company_id: companyId,
			tag_for: "ITEM"
		},
		attributes: ["tag_id", "name", "color", "tag_for", "company_id"],
		order: [["name", "ASC"]],
	})
	return tagList;
}


exports.genericQrListForJobList = async (companyId) => {
	return await qr_code.findAll({
		where:
		{
			company_id: companyId,
			type: "Generic"
		},
		attributes: [
			"qr_code_id",
			"type",
			"company_id",
			"job_id",
			["random_number", "qr_generate_code"],
			"label_number",
			[
				sequelize.literal(
					"(IF(`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
				),
				"is_qr_scanned",
			],

		],
		order: [["label_number", "ASC"]],
	})
}

exports.checkExternalLabelNumber = async () => {
	const data = await qr_code
		.findOne({
			where: {
				type: "External"
			},
			attributes: ["label_number"],
			order: [["label_number", "DESC"]],
			raw: true,
		});
	//newChanges
	if (data !== null) {
		return data
	}
	else {
		return { label_number: 0 }
	}
}

exports.findLastLabel = async (jobId) => {
	const data = await qr_code
		.findOne({
			where: {
				job_id: jobId,
				type: "Shipment"
			},
			attributes: ["label_number"],
			order: [["label_number", "DESC"]],
			raw: true,
		});
	//newChanges
	if (data !== null) {
		return data
	}
	else {
		return { label_number: 0 }
	}
}

exports.findJobFirstLabel = async (jobId) => {
	const data = await qr_code
		.findOne({
			where: {
				job_id: jobId,
				type: "Shipment"
			},
			attributes: ["label_number"],
			order: [["label_number", "ASC"]],
			raw: true,
		});
	if (data !== null) {
		return data.label_number
	}
	else {
		return 0
	}
}

exports.findJobLastLabel = async (jobId) => {
	const data = await qr_code
		.findOne({
			where: {
				job_id: jobId,
				type: "Shipment"
			},
			attributes: ["label_number"],
			order: [["label_number", "DESC"]],
			raw: true,
		});
	if (data !== null) {
		return data.label_number
	}
	else {
		return 0
	}
}

exports.qrListForAppApi = async (jobId, fieldsAndValues) => {
	return qr_code.findAndCountAll({
		limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
		offset:
			fieldsAndValues.page_no > 1
				? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
				: 0,
		where:
		{
			job_id: jobId,
			type: "Shipment",
			status: "Active",
			// [Op.or]: [
			// 	{ random_number: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
			// 	{ label_number: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
			// ],
		},

		attributes: [
			...COMMON_QR_ATTRIBUTES,
			[
				sequelize.literal(
					`(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
				),
				"qr_image",
			],
			[
				sequelize.literal(
					`LPAD(label_number, 8, 0)`
				),
				"label_number",
			],
			[
				sequelize.literal(

					`(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_id",
			],
			[
				sequelize.literal(

					`(SELECT deletedAt FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"deletedAt",
			],
			[
				sequelize.literal(
					`(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
				),
				"isQrScanned",
			],
			[
				sequelize.literal(
					`(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_name",
			],
		],
		order: [["label_number", "ASC"]],
	})
}

exports.listQrCodeModel = async (jobId, fieldsAndValues) => {
	return qr_code.findAndCountAll({
		limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
		offset:
			fieldsAndValues.page_no > 1
				? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
				: 0,
		where:
			jobId
				? {
					[Op.and]: [
						{
							job_id: jobId,
							type: "Shipment"
						},
						{
							[Op.or]: [
								{
									random_number: {
										[Op.like]: "%" + fieldsAndValues.search + "%",
									},
								},
								{
									label_number: {
										[Op.like]: "%" + fieldsAndValues.search + "%",
									},
								},
							],
						},
					],
					status: true,
				}
				: {
					[Op.or]: [
						{
							random_number: {
								[Op.like]: "%" + fieldsAndValues.search + "%",
							},
						},
						{
							label_number: { [Op.like]: "%" + fieldsAndValues.search + "%" },
						},
					],
					status: true,
				},
		attributes: [
			...COMMON_QR_ATTRIBUTES,
			[
				sequelize.literal(
					`(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
				),
				"qr_image",
			],
			[
				sequelize.literal(
					`LPAD(label_number, 8, 0)`
				),
				"label_number",
			],
			[
				sequelize.literal(

					`(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_id",
			],
			[
				sequelize.literal(

					`(SELECT deletedAt FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"deletedAt",
			],
			[
				sequelize.literal(
					`(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
				),
				"isQrScanned",
			],
			[
				sequelize.literal(
					`(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_name",
			],
		],
		include: [
			{
				model: shipment_job,
				as: "shipment_job",
				include: [
					{
						model: company,
						as: "job_company",
						attributes: [
							...COMMON_COMPANY_ATTRIBUTES,
						],
					},
				],
			},

		],
		order: [["label_number", "ASC"]],
	})
}

exports.listDymoQrCodeModel = async (jobId, fieldsAndValues, body) => {
	if (body.is_QrRange_select) {
		return qr_code.findAndCountAll({
			where: {
				job_id: jobId,
				label_number: { [Op.between]: [body.from_label, body.to_label] }
			},
			attributes: [
				...COMMON_QR_ATTRIBUTES,
				[
					sequelize.literal(
						`(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
					),
					"qr_image",
				],
				[
					sequelize.literal(
						`LPAD(label_number, 8, 0)`
					),
					"label_number",
				],
				[
					sequelize.literal(

						`(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"item_id",
				],
				[
					sequelize.literal(

						`(SELECT deletedAt FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"deletedAt",
				],
				[
					sequelize.literal(
						`(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
					),
					"isQrScanned",
				],
				[
					sequelize.literal(
						`(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"item_name",
				],
			],
			include: [
				{
					model: shipment_job,
					as: "shipment_job",
					include: [
						{
							model: company,
							as: "job_company",
							attributes: [
								...COMMON_COMPANY_ATTRIBUTES,
							],
						},
					],
				},

			],
			order: [["label_number", "ASC"]],
		})
	}
	else {
		return qr_code.findAndCountAll({
			where: {
				job_id: jobId
			},
			attributes: [
				...COMMON_QR_ATTRIBUTES,
				[
					sequelize.literal(
						`(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
					),
					"qr_image",
				],
				[
					sequelize.literal(
						`LPAD(label_number, 8, 0)`
					),
					"label_number",
				],
				[
					sequelize.literal(

						`(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"item_id",
				],
				[
					sequelize.literal(

						`(SELECT deletedAt FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"deletedAt",
				],
				[
					sequelize.literal(
						`(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
					),
					"isQrScanned",
				],
				[
					sequelize.literal(
						`(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
					),
					"item_name",
				],
			],
			include: [
				{
					model: shipment_job,
					as: "shipment_job",
					include: [
						{
							model: company,
							as: "job_company",
							attributes: [
								...COMMON_COMPANY_ATTRIBUTES,
							],
						},
					],
				},

			],
			order: [["label_number", "ASC"]],
		})
	}
}



exports.getQrCodeDetailsLinksModel = async (jobId, qrId) => {
	const value = await qr_code
		.findAll({
			where: { job_id: jobId, status: true, qr_code_id: qrId },
			attributes: [
				"qr_image",
				"random_number",
				[
					sequelize.literal(
						`LPAD(label_number, 8, 0)`
					),
					"label_number",
				],
			],
			include: [
				{
					model: shipment_job,
					as: "shipment_job",
					include: [
						{
							model: company,
							as: "job_company",
						},
					],
				},
			],
			order: [["label_number", "ASC"]],
		})

	if (value) {
		return JSON.parse(JSON.stringify(value, (k, v) => (v === null ? "" : v)))
	}
}

exports.listQrCodeLinksModel = async (jobId, body) => {
	if (body.is_DateRange_select) {
		const value = await qr_code
			.findAll({
				where: {
					job_id: jobId,
					status: true,
					label_number: { [Op.between]: [body.from_label, body.to_label] }
				},
				attributes: [
					"qr_image",
					"random_number",
					[
						sequelize.literal(
							`LPAD(label_number, 8, 0)`
						),
						"label_number",
					],
				],
				include: [
					{
						model: shipment_job,
						as: "shipment_job",
						include: [
							{
								model: company,
								as: "job_company",
							},
						],
					},
				],
				order: [["label_number", "ASC"]],
			})
		if (value) {
			return JSON.parse(JSON.stringify(value, (k, v) => (v === null ? "" : v)))
		}

	}
	else {
		const value = await qr_code
			.findAll({
				where: {
					job_id: jobId,
					status: true,
				},
				attributes: [
					"qr_image",
					"random_number",
					[
						sequelize.literal(
							`LPAD(label_number, 8, 0)`
						),
						"label_number",
					],
				],
				include: [
					{
						model: shipment_job,
						as: "shipment_job",
						include: [
							{
								model: company,
								as: "job_company",
							},
						],
					},
				],
				order: [["label_number", "ASC"]],
			})
		if (value) {
			return JSON.parse(JSON.stringify(value, (k, v) => (v === null ? "" : v)))
		}
	}


}

exports.findQrSetting = async (jobId) =>
	await qr_setting.findOne({ where: { shipment_job_id: jobId } });

exports.findQrSetting = async (jobId) =>
	await qr_setting.findOne({ where: { shipment_job_id: jobId } });

exports.saveQrSettings = async (jobId, body) =>
	await qr_setting.create({
		shipment_job_id: jobId,
		place: body.place,
		title_flag: body.title_flag,
		job_number_flag: body.job_number_flag,
		company_name_flag: body.company_name_flag,
		company_contact_flag: body.company_contact_flag,
		from_address_flag: body.from_address_flag,
		to_address_flag: body.to_address_flag,
		sequenced_label_flag: body.sequenced_label_flag,
		qr_code_label_flag: body.qr_code_label_flag,
		external_reference_flag: body.external_reference_flag
	});

exports.updateQrSettings = async (jobId, body) =>
	await qr_setting.update(
		{
			place: body.place,
			title_flag: body.title_flag,
			job_number_flag: body.job_number_flag,
			company_name_flag: body.company_name_flag,
			company_contact_flag: body.company_contact_flag,
			from_address_flag: body.from_address_flag,
			to_address_flag: body.to_address_flag,
			sequenced_label_flag: body.sequenced_label_flag,
			qr_code_label_flag: body.qr_code_label_flag,
			external_reference_flag: body.external_reference_flag
		},
		{ where: { shipment_job_id: jobId } }
	);


exports.generateQrModel = async (qrArray) => await qr_code.bulkCreate(qrArray);

exports.generateQrModelForManualLabel = async (data) => {
	const qrCodeObj = {
		job_id: data[0].job_id,
		random_number: data[0].random_number,
		qr_image: data[0].qr_image,
		label_number: data[0].label_number
	}
	return await qr_code.create(qrCodeObj)
}

exports.removeQrModel = async (qrId) =>
	await qr_code.destroy({ where: { qr_code_id: qrId, status: true } });

exports.getQrModel = async (qrId) =>
	await qr_code.findOne({
		where: { qr_code_id: qrId, status: true },
		attributes: ["qr_image"],
	});

exports.checkJobExistenceModel = async (jobId) => {
	const isJob = await shipment_job
		.findOne({
			where: { shipment_job_id: jobId },
			attributes: ["shipment_job_id"],
			raw: true,
		});
	if (isJob !== null) {
		return true
	}
	else {
		return false
	}
}

exports.isQrCodeUsedModel = async (qrId) => {
	const isValidQr = await qr_code
		.findOne({
			where: { qr_code_id: qrId },
			include: [
				{
					model: inventory,
					required: true,
					attributes: [],
				},
			],
			attributes: ["qr_code_id"],
			raw: true,
		});
	if (isValidQr === null) {
		return true
	}
	else {
		return false
	}

}

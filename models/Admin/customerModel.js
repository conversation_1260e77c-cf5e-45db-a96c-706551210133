const {
	shipment_job,
	customer,
	tag_customer,
	tag,
	sequelize,
	company,

} = require("../../database/schemas");
const StaffModel = require("../../models/Admin/staffModel");

const bcrypt = require("bcrypt");
const { Op, literal } = require("sequelize");

exports.fetchCustomerForStorage = async (body) => {
	return customer.findOne({
		where: {
			customer_id: body.customer_id
		},
		attributes: [
			"customer_id",
			"storage_customer_id",
			"status",
			"email",
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = customer.company_id) "
				),
				"integration_key",
			],
		]
	})
}

exports.getCustomerIdForStorage = async (body) => {
	return customer.findOne({
		where: {
			storage_customer_id: body.storage_customer_id
		}
	})
}


exports.getCustomerListActive = async (company_id, request) => {
	const status = request.filter ? request.filter : "active";
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

	return await customer.findAndCountAll({
		limit: parseInt(pageSize),
		offset: parseInt(start),
		where: company_id
			? {
				[Op.and]: [
					{ company_id: company_id },
					{
						[Op.or]: [
							// { customer_name: { [Op.like]: "%" + search + "%" } },
							{ first_name: { [Op.like]: "%" + search + "%" } },
							{ last_name: { [Op.like]: "%" + search + "%" } },
							{ email: { [Op.like]: "%" + search + "%" } },
							{ account_id: { [Op.like]: "%" + search + "%" } },
						],
					},
				],
				is_deleted: "0",
				storage_customer_id: { [Op.not]: null },
				status: status,
			}
			: {
				[Op.or]: [
					// { customer_name: { [Op.like]: "%" + search + "%" } },
					{ first_name: { [Op.like]: "%" + search + "%" } },
					{ last_name: { [Op.like]: "%" + search + "%" } },
					{ email: { [Op.like]: "%" + search + "%" } },
					{ account_id: { [Op.like]: "%" + search + "%" } },
				],
				is_deleted: "0",
				status: status,
			},
		order: [[orderBy, orderSequence]],
		attributes: [
			...COMMON_CUSTOMER_ATTRIBUTES,
			[
				sequelize.literal(
					"(SELECT company_name from `companies` where company_id = `customer`.company_id)"
				),
				"company_name",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' ELSE customer.photo END)`
				),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' WHEN customer.photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Customer_Profile}', 'original/', customer.photo) ELSE customer.photo END)`
				),
				"customer_profile",
			],
			// [sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`), "photo"],
		],
		distinct: true,
		include: [
			{
				model: tag_customer,
				as: "customer_tag",
				required: false,
				attributes: ["tag_id"],
				include: {
					model: tag,
					as: "m2m_customer_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},
		],
	});
};

exports.getCustomerList = async (company_id, request) => {
	const status = request.filter ? request.filter : "active";
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

	return await customer.findAndCountAll({
		limit: parseInt(pageSize),
		offset: parseInt(start),
		where: company_id
			? {
				[Op.and]: [
					{ company_id: company_id },
					{
						[Op.or]: [
							// { customer_name: { [Op.like]: "%" + search + "%" } },
							{ first_name: { [Op.like]: "%" + search + "%" } },
							{ last_name: { [Op.like]: "%" + search + "%" } },
							{ email: { [Op.like]: "%" + search + "%" } },
							{ account_id: { [Op.like]: "%" + search + "%" } },
						],
					},
				],
				is_deleted: "0",
				status: status,
			}
			: {
				[Op.or]: [
					// { customer_name: { [Op.like]: "%" + search + "%" } },
					{ first_name: { [Op.like]: "%" + search + "%" } },
					{ last_name: { [Op.like]: "%" + search + "%" } },
					{ email: { [Op.like]: "%" + search + "%" } },
					{ account_id: { [Op.like]: "%" + search + "%" } },
				],
				is_deleted: "0",
				status: status,
			},
		order: [[orderBy, orderSequence]],
		attributes: [
			...COMMON_CUSTOMER_ATTRIBUTES,
			[
				sequelize.literal(
					"(SELECT company_name from `companies` where company_id = `customer`.company_id)"
				),
				"company_name",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' ELSE customer.photo END)`
				),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN customer.photo IS NULL THEN '' WHEN customer.photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Customer_Profile}', 'original/', customer.photo) ELSE customer.photo END)`
				),
				"customer_profile",
			],
			// [sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`), "photo"],
		],
		distinct: true,
		include: [
			{
				model: tag_customer,
				as: "customer_tag",
				required: false,
				attributes: ["tag_id"],
				include: {
					model: tag,
					as: "m2m_customer_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},
		],
	});
};

exports.addCustomer = async (request, file) =>
	await customer.create(
		{
			first_name: request.first_name && request.first_name,
			last_name: request.last_name && request.last_name,
			photo: file !== "" ? file : null,
			address1: request.address1 && request.address1,
			address2: request.address2 && request.address2,
			city: request.city && request.city,
			state: request.state && request.state,
			zipCode: request.zipCode && request.zipCode,
			country: request.country && request.country,
			phone: request.phone && request.phone,
			phone2: request.phone2 ? request.phone2 : "",
			phone3: request.phone3 ? request.phone3 : "",
			country_code: request.country_code && request.country_code,
			email: request.email && request.email,
			email2: request.email2 ? request.email2 : "",
			account_id: request.account_id ? request.account_id : "",
			account_name: request.account_name ? request.account_name : "",
			sales_rep: request.sales_rep ? request.sales_rep : "",
			notes: request.notes ? request.notes : "",
			company_id: request.company_id && request.company_id,
			total_shipment: request.total_shipment !== "" ? request.total_shipment : 1,
		}
	);

exports.addCustomerStorage = async (request) =>
	await customer.create(
		{
			first_name: request.first_name && request.first_name,
			last_name: request.last_name && request.last_name,
			storage_company_id: request.storage_company_id && request.storage_company_id,
			storage_customer_id: request.storage_customer_id && request.storage_customer_id,
			address1: request.address1 && request.address1,
			address2: request.address2 && request.address2,
			city: request.city && request.city,
			state: request.state && request.state,
			zipCode: request.zipCode && request.zipCode,
			country: request.country && request.country,
			phone: request.phone && request.phone,
			phone2: request.phone2 ? request.phone2 : "",
			phone3: request.phone3 ? request.phone3 : "",
			country_code: request.country_code && request.country_code,
			email: request.email && request.email,
			email2: request.email2 ? request.email2 : "",
			account_id: request.account_id ? request.account_id : "",
			account_name: request.account_name ? request.account_name : "",
			sales_rep: request.sales_rep ? request.sales_rep : "",
			notes: request.notes ? request.notes : "",
			company_id: request.company_id && request.company_id,
			total_shipment: request.total_shipment !== "" ? request.total_shipment : 1,
		}
	);

exports.checkExistingCustomer = async (request) => {
	return customer.count({ where: { customer_id: request.customer_id } });
};

exports.editCustomer = async (request) => {
	let customerObj = {
		first_name: request.first_name && request.first_name,
		last_name: request.last_name && request.last_name,
		address1: request.address1 && request.address1,
		address2: request.address2 && request.address2,
		city: request.city && request.city,
		state: request.state && request.state,
		zipCode: request.zipCode && request.zipCode,
		country: request.country && request.country,
		phone: request.phone && request.phone,
		phone2: request.phone2 ? request.phone2 : "",
		phone3: request.phone3 ? request.phone3 : "",
		country_code: request.country_code && request.country_code,
		email: request.email && request.email,
		email2: request.email2 ? request.email2 : "",
		account_id: request.account_id ? request.account_id : "",
		account_name: request.account_name ? request.account_name : "",
		sales_rep: request.sales_rep ? request.sales_rep : "",
		notes: request.notes ? request.notes : "",
		company_id: request.company_id && request.company_id,
		total_shipment: request.total_shipment !== "" ? request.total_shipment : 1,
		updated_at: new Date(),
	};
	if (request.photo && request.photo !== "") {
		customerObj["photo"] = request.photo;
	} else if (request.retain_photo === "false") {
		customerObj["photo"] = null;
	}
	let customerDetails = await customer.update(
		customerObj,
		{ where: { customer_id: request.customer_id } }
	);
	if (request.email) {
		await shipment_job.update(
			{ email: request.email },
			{ where: { customer_id: request.customer_id } }
		);
	}
	return customerDetails;
};
exports.deleteCustomer = async (request) => {
	return await customer.update(
		{
			is_deleted: literal('CASE WHEN is_deleted = "0" THEN "1" ELSE "0" END'),
		},
		{ where: { customer_id: request.customer_id } }
	);
};
exports.changeCustomerStatus = async (request) => {
	return await customer.update(
		{
			status: literal('CASE WHEN status = "active" THEN "inactive" ELSE "active" END'),
		},
		{ where: { customer_id: request.customer_id } }
	);
};


exports.changeCustomerStatusStorage = async (request) => {
	if (request.isDeleted) {
		return await customer.update(
			{
				is_deleted: 1,
			},
			{ where: { storage_customer_id: request.customer_id } }
		);
	}
	else {
		return await customer.update(
			{
				status: request.isActive == 1 ? "active" : "inactive"
			},
			{ where: { storage_customer_id: request.customer_id } }
		);
	}
};

exports.viewCustomer = async (company_id, customer_id) => {
	return await customer.findOne({
		attributes: [
			...COMMON_CUSTOMER_ATTRIBUTES,
			[sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`), "photo"],
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' WHEN photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Customer_Profile}', 'original/', photo) ELSE photo END)`
				),
				"customer_profile",
			],
		],
		include: [
			{
				model: tag_customer,
				as: "customer_tag",
				required: false,
				attributes: [COMMON_CUSTOMER_TAG_ATTRIBUTES[0]],
				include: {
					model: tag,
					as: "m2m_customer_tag",
					attributes: COMMON_TAG_ATTRIBUTES,
				},
			},
		],
		// where: { customer_id: customer_id },
		where: company_id
			? { company_id: company_id, customer_id: customer_id }
			: { customer_id: customer_id }
		// transaction,
	});
};
exports.findCustomer = async (customer_id) => {
	return await customer.findOne({
		attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
		where: {
			customer_id: customer_id,
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
		},
		include: {
			model: company,
			as: "customer_company",
			attributes: ["company_name", "email", "phone"],
		},
		// raw: true,
	});
};

exports.checkReExistingEmailAddTime = async (request) => {
	return await customer.count({
		where: {
			email: request.email,
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
		},
	});
};


exports.checkReExistingEmail = async (request) => {
	return await customer.count({
		where: {
			email: request.email,
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
			customer_id: { [Op.ne]: request.customer_id },
		},
	});
};

exports.checkReExistingEmailAddEmail = async (request) => {
	return await customer.count({
		where: {
			email: request.email,
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
		},
	});
};

exports.checkReExistingEmailAdd = async (request) => {
	return await customer.findAll({
		where: {
			email: { [Op.in]: request.email },
			[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
		},
	});
};

exports.checkReExistingEmailEdit = async (request) => {
	console.log(request)
	return await customer.findAll({
		where: {
			email: { [Op.in]: request.email },

			[Op.or]: [
				{ storage_customer_id: { [Op.ne]: request.storage_customer_id } },
				{ storage_customer_id: { [Op.is]: null } },
			],
		},
	});
};

exports.isCustomerModel = async (customerId = null, email = null) => {
	const isCustomer = await customer
		.findOne({
			where: {
				[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
				[Op.or]: [{ email: email }, { customer_id: customerId }],
			},
			attributes: ["customer_id"],
			raw: true,
		});
	if (isCustomer !== null) {
		return true
	}
	else {
		return false
	}
}

exports.isValidActiveCustomerModel = async (email) => {
	return await customer.findOne({
		where: {
			email: email,
			is_deleted: "0"
		},
		attributes: ["customer_id", "email", "is_deleted", "status"]
	})
}

exports.isSignUpModel = async (customerId) => {
	//newChanges
	const isCustomer = await customer
		.findOne({
			where: {
				[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
				customer_id: customerId,
				[Op.or]: [{ password: null }, { password: "" }],
			},
			attributes: ["customer_id"],
			raw: true,
		});
	if (isCustomer !== null) {
		return true
	}
	else {
		return false
	}
}

exports.getCustomerId = async (email) => {
	//newChanges
	const customerDetails = await customer
		.findOne({
			where: {
				[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
				email: email,
			},
			attributes: ["customer_id"],
		});

	if (customerDetails) {
		return customerDetails.customer_id
	}
	else {
		return null
	}
}

exports.getCustomerBasicModelActive = async (filters, userDetails) => {

	if (userDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);

		const dbValue = await customer
			.findAll({
				where: {
					is_deleted: 0,
					storage_customer_id: { [Op.not]: null },
					company_id: getStaffDetails.company_id,
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + filters.search + "%" } },
					],
				},
				attributes: [
					"customer_id",
					[
						sequelize.literal(
							'CASE WHEN last_name is NULL then first_name ELSE CONCAT(first_name," ",last_name) end'
						),
						"full_name",
					],
					"email",
					"address1",
					"address2",
					"city",
					"state",
					"zipCode",
					"country",
					"company_id",
					"storage_customer_id",
					"status"
				],
				order: [["first_name"]],
			});
		if (dbValue) {
			return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)))
		}
	}
	else {
		const dbValue = await customer
			.findAll({
				where: {
					is_deleted: 0,
					storage_customer_id: { [Op.not]: null },
					company_id: userDetails.company_id,
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + filters.search + "%" } },
					],
				},
				attributes: [
					"customer_id",
					[
						sequelize.literal(
							'CASE WHEN last_name is NULL then first_name ELSE CONCAT(first_name," ",last_name) end'
						),
						"full_name",
					],
					"email",
					"address1",
					"address2",
					"city",
					"state",
					"zipCode",
					"country",
					"company_id",
					"storage_customer_id",
					"status"
				],
				order: [["first_name"]],
			});
		if (dbValue) {
			return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)))
		}
	}
}

exports.getCustomerBasicModel = async (filters, userDetails) => {

	if (userDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);

		const dbValue = await customer
			.findAll({
				where: {
					is_deleted: 0,
					company_id: getStaffDetails.company_id,
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + filters.search + "%" } },
					],
				},
				attributes: [
					"customer_id",
					[
						sequelize.literal(
							'CASE WHEN last_name is NULL then first_name ELSE CONCAT(first_name," ",last_name) end'
						),
						"full_name",
					],
					"email",
					"address1",
					"address2",
					"city",
					"state",
					"zipCode",
					"country",
					"company_id",
					"storage_customer_id",
					"status"
				],
				order: [["first_name"]],
			});
		if (dbValue) {
			return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)))
		}
	}
	else {
		const dbValue = await customer
			.findAll({
				where: {
					is_deleted: 0,
					company_id: userDetails.company_id,
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + filters.search + "%" } },
					],
				},
				attributes: [
					"customer_id",
					[
						sequelize.literal(
							'CASE WHEN last_name is NULL then first_name ELSE CONCAT(first_name," ",last_name) end'
						),
						"full_name",
					],
					"email",
					"address1",
					"address2",
					"city",
					"state",
					"zipCode",
					"country",
					"company_id",
					"storage_customer_id",
					"status"
				],
				order: [["first_name"]],
			});
		if (dbValue) {
			return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)))
		}
	}
}

exports.customerStorageIdUpdate = async (request) => {
	try {
		return customer.update(
			{
				storage_customer_id: request.customerIdStorage
			},
			{ where: { customer_id: request.customer_id } }
		);
	}
	catch (err) {
		console.log("err", err);
	}
}

exports.addTagToCustomerModel = (tags, customerId) => {
	tag_customer.destroy({ where: { customer_id: customerId } });
	tag_customer.bulkCreate(tags);
};

exports.updateCustomerInviteStatusCustomerModel = (customer_id, status) => {
	return customer.update(
		{ is_invited: status },
		{
			where: {
				[Op.and]: [
					{ customer_id: customer_id },
					{
						[Op.or]: [{ is_invited: { [Op.ne]: "REGISTERED" } }, { is_invited: null }],
					},
				],
			},
		}
	);
};
exports.updateCustomerPasswordStatusCustomerModel = async (customer_id) => {
	//newChanges
	const data = await customer
		.update(
			{ passwordTokenExpire: Date.now() + 900000 },
			{
				where: {
					customer_id: customer_id,
				},
			}
		);
	if (data) {
		return data;
	}
	else {
		return null
	}
};
exports.signIn = async (id) => {
	return await customer.findOne({
		where: {
			customer_id: id,
		},
		raw: true,
	});
};
exports.updateCustomerPasswordModel = async (customer_id, password) => {
	const hashed_password = bcrypt.hashSync(password, bcrypt.genSaltSync(10));
	return await customer.update(
		{ password: hashed_password, is_invited: "REGISTERED" },
		{
			where: {
				customer_id: customer_id,
			},
		}
	);
};
exports.updateCustomerPasswordModelForForget = async (customer_id, password) => {
	const hashed_password = bcrypt.hashSync(password, bcrypt.genSaltSync(10));
	return await customer.update(
		{ password: hashed_password },
		{
			where: {
				customer_id: customer_id,
				passwordTokenExpire: {
					[Op.gt]: Date.now(),
				},
			},
		}
	);
};

exports.getCustomerPasswordAndShipmentDetailModel = async ({ email }) => {
	//newChanges
	const dbValue = await customer
		.findOne({
			where: {
				[Op.or]: [{ is_deleted: "0" }, { is_deleted: 0 }, { is_deleted: null }],
				email: email,
			},
			include: {
				model: shipment_job,
				attributes: ["shipment_job_id", "shipment_name", "job_number"],
			},
			attributes: ["password"],
		})
	if (dbValue) {
		return JSON.parse(JSON.stringify(dbValue, (k, v) => (v === null ? "" : v)));
	}
}

exports.checkCustomerAssignToJob = async (id) => {
	return await shipment_job.findAndCountAll({
		where: {
			customer_id: id,
		},
	});
};

exports.checkCustomerAssignToJobForStatus = async (id) => {
	return await shipment_job.findAndCountAll({
		where: {
			customer_id: id,
			is_job_complete_flag: 0
		},
	});
};

exports.findOldStatusOfCustomer = async (id) => {
	return await customer.findOne({
		where: {
			customer_id: id,
		},
		attributes: ["status"]
	});
};

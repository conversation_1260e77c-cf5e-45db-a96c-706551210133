"use strict";
module.exports = {
    up: async (queryInterface, Sequelize) => {
        return queryInterface.addColumn(
            'shipment_inventory_forced',
            'shipment_job_id',
            {
                type: Sequelize.INTEGER(11),
                allowNull: true,
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
                references: {
                    model: "shipment_jobs",
                    key: "shipment_job_id",
                },
            }
        )    },
    down: async (queryInterface, Sequelize) => {
        return queryInterface.removeColumn('shipment_inventory_forced', 'shipment_job_id')
    },
};

"use strict";
module.exports = (sequelize, DataTypes) => {
  const warehouse_list_staff = sequelize.define(
    "warehouse_list_staff",
    {
      warehouse_list_staff_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      staff_id: DataTypes.INTEGER,
      warehouse_id: DataTypes.STRING,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE
      }
    },
    { createdAt: false, updatedAt: false, paranoid: true }
  );
  warehouse_list_staff.associate = function (models) {
    warehouse_list_staff.belongsTo(models.staff, { foreignKey: 'staff_id' });

  };
  return warehouse_list_staff;
};

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_rooms',
        'iso_code',
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_rooms', 'iso_code');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

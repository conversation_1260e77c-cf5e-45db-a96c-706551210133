const smtpTransport = require("nodemailer-smtp-transport");
const nodemailer = require("nodemailer");
const generalModel = require("../../models/APP/generalModel");
const commonFunction = require("../../assets/common");

exports.getStaticPage = async (request, response) => {
	try {
		const pageData = await generalModel.getPageData(request.body.page_id);
		if (pageData !== "" && pageData !== null) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: PAGE_SUCCESS,
				data: pageData,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: PAGE_FAIL,
				data: {},
			});
		}
	} catch (e) {
		console.log("exports.getStaticPage -> error: ", e);

		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};

exports.contactUs = async (request, response) => {
	try {
		let html = await commonFunction.readFile("contactUs.html");

		html = html.replace(/{{name}}/g, request.body.name);
		html = html.replace(/{{email}}/g, request.body.email);
		html = html.replace(/{{message}}/g, request.body.message);

		let isEmailSent = await commonFunction.sendEmail(Const_Admin_Email, "Contact Us", html);
		if (isEmailSent === false) {
			throw Error("Can't send you request,please try again letter.");
		} else {
			try {
				const contactUsData = await generalModel.contactUs(request.body);
				if (contactUsData != null) {
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: CONTACT_EMAIL_SENT,
						data: {},
					});
				}
			}
			catch (e) {
				response.status(SERVER_ERROR_CODE).json({
					status: 0,
					message: e.message,
					data: {},
				});
			}
		}
	} catch (e) {
		console.log("exports.contactUs -> error: ", e);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};

exports.feedback = async (request, response) => {
	try {
		let html = await commonFunction.readFile("feedback.html");
		html = html.replace(/{{experience}}/g, request.body.experience);
		html = html.replace(/{{feedback}}/g, request.body.feedback);
		let isEmailSent = await commonFunction.sendEmail(Const_Admin_Email, "Feedback", html);
		if (isEmailSent === false) {
			throw Error("Can't send your feedback,please try again letter.");
		} else {
			try {
				const feedbackData = await generalModel.feedback(request.body);

				if (feedbackData != null) {
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: FEEDBACK_EMAIL_SENT,
						data: {},
					});
				}
			} catch (e) {
				response.status(SERVER_ERROR_CODE).json({
					status: 0,
					message: e.message,
					data: {},
				});
			}
		}
	} catch (e) {
		console.log("exports.feedback -> error: ", e);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: e.message,
			data: {},
		});
	}
};

exports.sendRequestMail = async (req, res) => {
	try {
		req.body.remark = req.body.remark ? req.body.remark : " - ";
		const { first_name, last_name, email, phone, company_name, remark } = req.body;
		let html = await commonFunction.readFile("sendRequest.html");
		html = html.replace(/{{full_name}}/g, first_name + " " + last_name);
		html = html.replace(/{{email}}/g, email);
		html = html.replace(/{{phone}}/g, phone);
		html = html.replace(/{{company_name}}/g, company_name);
		html = html.replace(/{{remark}}/g, remark);
		let transporter = nodemailer.createTransport(
			smtpTransport({
				host: Const_Email_Host,
				port: Const_Email_Port,
				secure: false,
				service: "gmail",
				//service: 'ses-smtp-user.20190212-100617',
				auth: {
					user: Const_Email_id, // generated ethereal user
					pass: Const_Email_Password, // generated ethereal password
				},
			})
		);
		let mailOptions = {
			from: '"Mover Inventory"< ' + Const_Email + ">", // sender address
			to: "<EMAIL>", // "<EMAIL>" list of receivers
			subject: "App request for mover inventory", // Subject line
			//text: 'Hello All? Senbd fron Node.js!', // plain text body
			html: html,
		};
		if (typeof CCEmailId !== "undefined" && CCEmailId && CCEmailId.length > 0) {
			mailOptions.cc = CCEmailId;
		}
		transporter.sendMail(mailOptions, (error, info) => {
			try {
				if (error) {
					throw error;
				}
				commonFunction.generateResponse(
					res,
					SUCCESS_CODE,
					1,
					"Thanks, your request has been sent!",
					{}
				);
			} catch (e) {
				commonFunction.generateResponse(
					res,
					SERVER_ERROR_CODE,
					0,
					"Error in your sending request. Please try again later!",
					{}
				);
			}
		});
	} catch (error) {
		console.log("exports.sendRequestMail -> error: ", error);
		commonFunction.generateResponse(
			res,
			SERVER_ERROR_CODE,
			0,
			"Error in your sending request. Please try again later!",
			{}
		);
	}
};

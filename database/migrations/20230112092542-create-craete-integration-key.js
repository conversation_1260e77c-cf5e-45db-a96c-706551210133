"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("company_integration_keys", {
      company_integration_key_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "companies",
          key: "company_id",
        },
      },
      integration_key: {
        type: Sequelize.STRING,
        allowNull: false
      },
      company_identity: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "inactive",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("company_integration_keys");
  },
};

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'staffs',
        'is_allow_view_item',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 1,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("staffs", "is_allow_view_item");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
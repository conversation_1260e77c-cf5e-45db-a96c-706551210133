"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn(
				"shipment_inventory_job_scanned",
				"scanned_by_staff",
				{
					allowNull: true,
					type: Sequelize.INTEGER(11),
					onDelete: "CASCADE",
					onUpdate: "CASCADE",
					references: {
						model: "staffs",
						key: "staff_id",
					},
				}
			);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn(
				"shipment_inventory_job_scanned",
				"scanned_by_staff"
			);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

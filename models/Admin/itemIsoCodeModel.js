const { item_iso_code } = require("../../database/schemas");
const { Op } = require("sequelize");

// Get all item ISO codes
exports.getAllItemIsoCodes = async () => {
	return await item_iso_code.findAll({
		attributes: ['id', 'name', 'code'],
		raw: true
	});
};

// Create single item ISO code
exports.createItemIsoCode = async (itemIsoData) => {
	return await item_iso_code.create({
		name: itemIsoData.name,
		code: itemIsoData.code,
		created_at: new Date(),
		updated_at: new Date()
	});
};

// Bulk create item ISO codes
exports.bulkCreateItemIsoCodes = async (itemIsoDataArray) => {
	// Prepare data for bulk insert
	const bulkData = itemIsoDataArray.map(item => ({
		name: item.name,
		code: item.code,
		created_at: new Date(),
		updated_at: new Date()
	}));
	
	return await item_iso_code.bulkCreate(bulkData);
};

// Clear all item ISO codes (for fresh import)
exports.clearAllItemIsoCodes = async () => {
	return await item_iso_code.destroy({
		where: {},
		truncate: true
	});
};

// Get item ISO code by ID
exports.getItemIsoCodeById = async (id) => {
	return await item_iso_code.findByPk(id);
};

// Update item ISO code
exports.updateItemIsoCode = async (id, updateData) => {
	return await item_iso_code.update(
		{
			name: updateData.name,
			code: updateData.code,
			updated_at: new Date()
		},
		{ where: { id: id } }
	);
};

// Delete item ISO code
exports.deleteItemIsoCode = async (id) => {
	return await item_iso_code.destroy({
		where: { id: id }
	});
};

// Check if item ISO code exists by code
exports.checkItemIsoCodeExists = async (code) => {
	const count = await item_iso_code.count({
		where: { code: code }
	});
	return count > 0;
};

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_inventory_comments",
        "shipment_inventory_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_inventories",
            key: "shipment_inventory_id",
          },
        }
      );

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventory_comments", "shipment_inventory_id");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

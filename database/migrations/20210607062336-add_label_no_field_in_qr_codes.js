"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("qr_codes", "label_number", {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("qr_codes", "label_number");
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

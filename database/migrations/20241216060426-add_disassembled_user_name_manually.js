"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'disassembled_user_name_manually',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
      );
      await queryInterface.addColumn(
        'shipment_inventories',
        'prepared_user_name_manually',
        {
          type: Sequelize.STRING,
          allowNull: true,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "disassembled_user_name_manually");
      await queryInterface.removeColumn("shipment_inventories", "prepared_user_name_manually");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
const staffModelApi = require("../../models/Api/staffModel");


/**Post /staff/list staffList Controller */
exports.staffListController = async (request, response) => {
    try {
        //here we fetch staffList
        const staffList = await staffModelApi.getStaffList(request.query, request.body);
        if (staffList) {
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: STAFF_RETRIEVED_SUCCESS,
                data: staffList,
            });
        } else {
            response.status(EXPECTATION_FAILED_CODE).json({
                status: 0,
                message: STAFF_NOT_FOUND,
                data: {},
            });
        }
    } catch (error) {
        console.log("exports.getStaffList -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};
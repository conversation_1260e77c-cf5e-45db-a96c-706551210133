const ExcelJS = require("exceljs");
const path = require("path");

async function readExcelFile() {
    try {
        const filePath = path.join(__dirname, "docs", "shipment_rooms.xlsx");
        console.log("Reading Excel file from:", filePath);
        
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            console.log("No worksheet found");
            return;
        }
        
        console.log("Worksheet name:", worksheet.name);
        console.log("Row count:", worksheet.rowCount);
        console.log("Column count:", worksheet.columnCount);
        console.log("\nData preview:");
        console.log("Row | Column A (Room Name) | Column B (ISO Code)");
        console.log("-".repeat(50));
        
        let rowCount = 0;
        worksheet.eachRow((row, rowNumber) => {
            if (rowCount < 10) { // Show first 10 rows
                const roomName = row.getCell(1).value;
                const isoCode = row.getCell(2).value;
                console.log(`${rowNumber.toString().padStart(3)} | ${String(roomName || '').padEnd(20)} | ${String(isoCode || '')}`);
                rowCount++;
            }
        });
        
        // Extract all room data
        const excelRoomData = [];
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1) { // Skip header row
                const roomName = row.getCell(1).value;
                const isoCode = row.getCell(2).value;
                
                if (roomName && isoCode) {
                    excelRoomData.push({
                        name: String(roomName).trim(),
                        iso_code: String(isoCode).trim()
                    });
                }
            }
        });
        
        console.log(`\nTotal valid room entries found: ${excelRoomData.length}`);
        console.log("\nSample room data:");
        excelRoomData.slice(0, 5).forEach((room, index) => {
            console.log(`${index + 1}. ${room.name} -> ${room.iso_code}`);
        });
        
    } catch (error) {
        console.error("Error reading Excel file:", error.message);
    }
}

readExcelFile();

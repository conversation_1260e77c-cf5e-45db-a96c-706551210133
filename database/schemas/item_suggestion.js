"use strict";
module.exports = (sequelize, DataTypes) => {
	const item_suggestion = sequelize.define(
		"item_suggestion",
		{
			item_suggestion_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			createdByUserRole: DataTypes.STRING,

			admin_id:
			{
				type: DataTypes.INTEGER,
			},
			company_id: {
				type: DataTypes.INTEGER,
			},
			staff_id: {
				type: DataTypes.INTEGER,
			},

			volume: {
				type: DataTypes.DECIMAL(11, 2),
				defaultValue: 0,
			},
			weight: {
				type: DataTypes.DECIMAL(11, 2),
				defaultValue: 0,
			},
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false, freezeTableName: true }
	);
	item_suggestion.associate = function (models) {


	};
	return item_suggestion;
};

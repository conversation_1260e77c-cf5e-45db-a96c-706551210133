const { check, query, param, body } = require("express-validator");
const moment = require("moment");
const dateRegex =
	/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;
let falsify = { checkFalsy: true };

module.exports = {
	app: {
		staffSignIn: [
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("Please enter password.")
				.isLength({ min: 6 })
				.withMessage("Password must be atleast 6 characters long."),
			check("device_type").exists().withMessage("Please enter device type."),
			check("version").exists().withMessage("Please enter version."),
		],

		signInWithcompanyId: [
			check("company_id").exists().withMessage("Please enter company id."),
			check("device_type").exists().withMessage("Please enter device type."),
			check("version").exists().withMessage("Please enter version."),
		],

		staffForgotPassword: [
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
		],

		globalQrCodeValidation: [
			param("random_number")
				.exists()
				.withMessage("Please enter random_number.")
				.notEmpty()
				.withMessage("random_number cannot be blank.")
				.isString()
				.trim()
				.withMessage(`random_number only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`random_number can only contain alphanumeric.`),
		],

		checkQrCodeValidation: [
			param("random_number")
				.exists()
				.withMessage("Please enter random_number.")
				.notEmpty()
				.withMessage("random_number cannot be blank.")
				.isLength({ min: 5, max: 30 })
				.withMessage('random_number min length should be 5 and max length should be 30')
				.isString()
				.trim()
				.withMessage(`random_number only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`random_number can only contain alphanumeric.`),
		],

		allItemsListValidation: [
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("tagName")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Tag name field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
			param("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("ShipmentId cannot be blank.")
		],

		allItemsListValidationForCms: [
			check("stageName")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("stage name fields cannot be blank.")
				.isIn([
					"isScannedFlag",
					"is_item_scanned_remove_from_storage",
					"is_additional_scan",
					"is_remove_scan",
					"is_item_add_to_storage"
				])
				.withMessage("stage name fields value is incorrect."),
			check("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			check("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([
					...COMMON_INVENTORY_ATTRIBUTES,
					"is_additional_scan",
					"is_remove_scan"
				])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("ShipmentId cannot be blank.")
		],

		allItemsListValidationForCmsWeb: [
			check("currentShipmentStageName")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("stage name fields cannot be blank.")
				.isIn([
					"add_items_to_inventory",
					"add_items_to_storage",
					"remove_items_from_storage",
					"remove_items_from_inventory",
					"remove_scan_require",
					"additional_scan_require"
				])
				.withMessage("stage name fields value is incorrect."),
			check("stageName")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("stage name fields cannot be blank.")
				.isIn([
					"isScannedFlag",
					"is_item_scanned_remove_from_storage",
					"is_additional_scan",
					"is_remove_scan",
					"is_item_add_to_storage"
				])
				.withMessage("stage name fields value is incorrect."),
			check("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			check("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([
					...COMMON_INVENTORY_ATTRIBUTES,
					"is_additional_scan",
					"is_remove_scan"
				])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("ShipmentId cannot be blank.")
		],


		fetchAllUnitListArray: [
			check("unitList")
				.exists()
				.withMessage("Please enter unitList.")
				.isArray()
				.withMessage("UnitList must be an array."),
		],

		shuffleAndReshuffleItem: [
			check("itemIds")
				.exists()
				.withMessage("Please enter itemIds.")
				.isArray()
				.withMessage("ItemIds must be an array."),
			check("unitId")
				.exists()
				.withMessage("Please enter unitId.")
				.notEmpty()
				.withMessage("unitId cannot be blank.")
		],

		deliverShipment: [
			check("unitIds")
				.exists()
				.withMessage("Please enter unitIds.")
				.isArray()
				.withMessage("unitIds must be an array."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("shipmentId cannot be blank.")
				.isInt()
				.withMessage("shipmentId only accept numeric value."),
			check("isCompleteDelivery")
				.exists()
				.withMessage("Please enter is complete delivery.")
				.isIn([true, false])
				.withMessage("Is complete delivery value is not valid."),
		],

		fetchAllItemListByWarehouse: [
			check("warehouseId")
				.exists()
				.withMessage("Please enter warehouseId.")
				.notEmpty()
				.withMessage("warehouse id cannot be blank."),
			check("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			body("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			body("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			body("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES])
				.withMessage("ordering fields value is incorrect."),
			body("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
		],

		fetchAllUnitListByArray: [
			check("unitArray")
				.exists()
				.withMessage("Please enter unitArray.")
				.isArray()
				.withMessage("UnitArray must be an array."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
		],

		fetchAllUnitCountValidation: [
			check("unitId")
				.exists()
				.withMessage("Please enter unitId.")
				.notEmpty()
				.withMessage("unitId cannot be blank."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("shipmentId cannot be blank.")
				.isInt()
				.withMessage("shipmentId only accept numeric value."),
		],

		itemsHistoryByArray: [
			check("unitArray")
				.exists()
				.withMessage("Please enter unitArray.")
				.isArray()
				.withMessage("UnitArray must be an array."),
			check("shipmentArray")
				.exists()
				.withMessage("Please enter shipmentArray.")
				.isArray()
				.withMessage("ShipmentArray must be an array."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
		],

		itemsHistoryCountByUnits: [
			check("unitArray")
				.exists()
				.withMessage("Please enter unitArray.")
				.isArray()
				.withMessage("UnitArray must be an array."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("shipmentId cannot be blank.")
				.isInt()
				.withMessage("shipmentId only accept numeric value."),
		],

		itemsHistoryCountByShipments: [
			check("shipmentArray")
				.exists()
				.withMessage("Please enter shipmentArray.")
				.isArray()
				.withMessage("ShipmentArray must be an array."),
			check("unitId")
				.exists()
				.withMessage("Please enter unitId.")
				.notEmpty()
				.withMessage("unitId cannot be blank.")
		],

		fetchShipmentArray: [
			check("shipmentArray")
				.exists()
				.withMessage("Please enter shipmentArray.")
				.isArray()
				.withMessage("ShipmentArray must be an array."),
		],

		getStaticPage: [
			check("page_id")
				.exists()
				.withMessage("Please enter page id.")
				.notEmpty()
				.withMessage("Page id cannot be blank.")
				.isInt()
				.withMessage("Page id only accept numeric value."),
		],

		staffChangePassword: [
			check("old_password")
				.exists()
				.withMessage("Please enter password."),
			check("new_password")
				.exists()
				.withMessage("Please enter new password.")
				.isLength({ min: 8 })
				.withMessage("New password must be at least 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"New Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
		],

		staffChangePasswordStorage: [
			check("new_password")
				.exists()
				.withMessage("Please enter new password.")
				.isLength({ min: 8 })
				.withMessage("New password must be at least 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"New Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
			check("staffId")
				.exists()
				.withMessage("Please enter staffId.")
				.notEmpty()
				.withMessage("staffId cannot be blank."),
		],


		contactUs: [
			check("name")
				.exists()
				.withMessage("Please enter name.")
				.notEmpty()
				.withMessage("Name cannot be blank."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("message")
				.exists()
				.withMessage("Please enter message.")
				.notEmpty()
				.withMessage("Message cannot be blank."),
		],

		feedback: [
			check("feedback")
				.exists()
				.withMessage("Please enter feedback.")
				.notEmpty()
				.withMessage("Feedback cannot be blank."),
		],

		updateItemThumbnail: [
			check("inventory_photo_id")
				.exists()
				.withMessage("Please enter photo id.")
				.notEmpty()
				.withMessage("photoId cannot be blank.")
				.isInt()
				.withMessage("photoId only accept numeric value."),
			check("inventory_id")
				.exists()
				.withMessage("Please enter shipment inventory id.")
				.notEmpty()
				.withMessage("Shipment inventory id cannot be blank.")
				.isInt()
				.withMessage("Shipment inventory id only accept numeric value."),
		],

		companyListByGroup: [
			check("group_id")
				.exists()
				.withMessage("Please enter group id.")
				.notEmpty()
				.withMessage("groupId cannot be blank.")
				.isInt()
				.withMessage("groupId only accept numeric value."),
			check("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			check("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
		],


		companyListSuperAdmin: [
			check("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			check("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
		],

		addWarehouseMan: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("shipmentId cannot be blank.")
				.isInt()
				.withMessage("shipmentId only accept numeric value."),
			check("staffId")
				.exists()
				.withMessage("Please enter staffId.")
				.notEmpty()
				.withMessage("staffId cannot be blank.")
				.isInt()
				.withMessage("staffId only accept numeric value."),
			check("unitId")
				.exists()
				.withMessage("Please enter unitId.")
				.notEmpty()
				.withMessage("unitId cannot be blank.")
				.isInt()
				.withMessage("unitId only accept numeric value."),
			check("stageId")
				.exists()
				.withMessage("Please enter stageId.")
				.notEmpty()
				.withMessage("stageId cannot be blank.")
				.isInt()
				.withMessage("stageId only accept numeric value."),
			check("storageUnitId")
				.exists()
				.withMessage("Please enter storageUnitId.")
				.notEmpty()
				.withMessage("storageUnitId cannot be blank.")
		],

		checkUnitValidationInventory: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("shipmentId cannot be blank.")
				.isInt()
				.withMessage("shipmentId only accept numeric value."),
			check("storage_unit_id")
				.exists()
				.withMessage("Please enter storageUnitId.")
				.notEmpty()
				.withMessage("storageUnitId cannot be blank.")
		],

		addItem: [
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("room_id")
				.exists()
				.withMessage("Please enter room id.")
				.notEmpty()
				.withMessage("Room id cannot be blank.")
				.isInt()
				.withMessage("Room id only accept numeric value."),
			check("qr_id")
				.optional(true),
			check(`qr_generate_code`)
				.optional(falsify)
				.exists()
				.withMessage(`Please enter qr generate code`)
				.notEmpty()
				.withMessage(`Qr generate code cannot be blank.`)
				.isString()
				.trim()
				.withMessage(`qr generate code only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`Qr generate code can only contain alphanumeric.`),
			check("item_name").exists().withMessage("Please enter item name."),
			check("desc").exists().withMessage("Please enter description."),
			check("is_carton")
				.exists()
				.withMessage("Please enter is carton status.")
				.isIn([true, false])
				.withMessage("Is carton value is not valid."),
			check("packed_by").optional(true),
			check("packed_user_id").optional(true),
			check("is_disassembled")
				.exists()
				.withMessage("Please enter is disassembled status.")
				.isIn([true, false])
				.withMessage("Is disassembled value is not valid."),
			check("disassembled_by").optional(true),
			check("disassembled_user_id").optional(true),
			check("is_electronics")
				.exists()
				.withMessage("Please enter is electronics status.")
				.isIn([true, false])
				.withMessage("Is electronics value is not valid."),
			check("serial_number").optional(true),
			check("is_high_value")
				.exists()
				.withMessage("Please enter is high value status.")
				.isIn([true, false])
				.withMessage("Is high value is not valid."),
			check("declared_value").optional(true),
			check("is_pro_gear")
				.exists()
				.withMessage("Please enter is pro gear status.")
				.isIn([true, false])
				.withMessage("Is pro gear value is not valid."),
			check("pro_gear_weight").optional(true),
			check("progear_name").optional(true),
			check("is_firearm")
				.optional(true)
				.isIn([true, false])
				.withMessage("Is firearm value is not valid."),
			check("firmarm_serial_number").optional(true),
			check("seal_number").optional(true),
			check("isManualLabel")
				.optional(true)
				.isIn([true, false])
				.withMessage("Is ManualLabel value is not valid."),
			check("label_no").optional(true),
			check("lot_no").optional(true),
			check("color").optional(true),
			check("comment").optional(true),
			check("inventory_notes").optional(true),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],

		storageLogin: [
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("Please enter password.")
				.isLength({ min: 6 })
				.withMessage("Password must be atleast 6 characters long."),
		],

		dateRequire: [
			check("date")
				.exists()
				.withMessage("Please enter date.")
		],

		editItemUnit: [
			check("stageId")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("inventory_id")
				.exists()
				.withMessage("Please enter shipment inventory id.")
				.notEmpty()
				.withMessage("Shipment inventory id cannot be blank.")
				.isInt()
				.withMessage("Shipment inventory id only accept numeric value."),
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("unit_id")
				.exists()
				.withMessage("Please enter unit id.")
				.notEmpty()
				.withMessage("Unit id cannot be blank.")
				.isInt()
				.withMessage("Unit id only accept numeric value."),
			check("storage_unit_id")
				.exists()
				.withMessage("Please enter storage unit id.")
				.notEmpty()
				.withMessage("Storage unit id cannot be blank."),
		],


		assignitemUnit: [
			check("stageId")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("random_number")
				.exists()
				.withMessage("Please enter random number.")
				.notEmpty()
				.withMessage("Random number cannot be blank.")
				.isString()
				.trim()
				.withMessage(`Random number only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`Random number can only contain alphanumeric.`),
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("unit_id")
				.exists()
				.withMessage("Please enter unit id.")
				.notEmpty()
				.withMessage("Unit id cannot be blank.")
				.isInt()
				.withMessage("Unit id only accept numeric value."),
			check("storage_unit_id")
				.exists()
				.withMessage("Please enter storage unit id.")
				.notEmpty()
				.withMessage("Storage unit id cannot be blank."),
		],

		bulkUnassignItemUnit: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("itemArray")
				.exists()
				.withMessage("Please enter itemArray.")
				.isArray()
				.withMessage("itemArray must be an array."),
			check("shipment_id")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("shipment id cannot be blank.")
				.isInt()
				.withMessage("shipment id id only accept numeric value."),
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("staff_id")
				.exists()
				.withMessage("Please enter Staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],

		bulkAssignItemUnit: [
			check("stageId")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("itemArray")
				.exists()
				.withMessage("Please enter itemArray.")
				.isArray()
				.withMessage("itemArray must be an array."),
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("unit_id")
				.exists()
				.withMessage("Please enter unit id.")
				.notEmpty()
				.withMessage("Unit id cannot be blank.")
				.isInt()
				.withMessage("Unit id only accept numeric value."),
			check("storage_unit_id")
				.exists()
				.withMessage("Please enter storage unit id.")
				.notEmpty()
				.withMessage("Storage unit id cannot be blank."),
		],

		checkItemsValidationUsingRandomNumber: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("random_number")
				.exists()
				.withMessage("Please enter random number.")
				.notEmpty()
				.withMessage("Random number cannot be blank.")
				.isString()
				.trim()
				.withMessage(`Random number only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`Random number can only contain alphanumeric.`),
		],

		editItem: [
			check("inventory_id")
				.exists()
				.withMessage("Please enter shipment inventory id.")
				.notEmpty()
				.withMessage("Shipment inventory id cannot be blank.")
				.isInt()
				.withMessage("Shipment inventory id only accept numeric value."),
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("room_id")
				.exists()
				.withMessage("Please enter room id.")
				.notEmpty()
				.withMessage("Room id cannot be blank.")
				.isInt()
				.withMessage("Room id only accept numeric value."),
			check("qr_id")
				.optional(true),
			check("item_name").exists().withMessage("Please enter item name."),
			check("desc").exists().withMessage("Please enter description."),
			check("is_carton")
				.exists()
				.withMessage("Please enter is carton status.")
				.isIn([true, false])
				.withMessage("Is carton value is not valid."),
			check("packed_by").optional(true),
			check("packed_user_id").optional(true),
			check("is_disassembled")
				.exists()
				.withMessage("Please enter is disassembled status.")
				.isIn([true, false])
				.withMessage("Is disassembled value is not valid."),
			check("disassembled_by").optional(true),
			check("disassembled_user_id").optional(true),
			check("is_electronics")
				.exists()
				.withMessage("Please enter is electronics status.")
				.isIn([true, false])
				.withMessage("Is electronics value is not valid."),
			check("serial_number").optional(true),
			check("is_high_value")
				.exists()
				.withMessage("Please enter is high value status.")
				.isIn([true, false])
				.withMessage("Is high value is not valid."),
			check("declared_value").optional(true),
			check("is_pro_gear")
				.exists()
				.withMessage("Please enter is pro gear status.")
				.isIn([true, false])
				.withMessage("Is pro gear value is not valid."),
			check("pro_gear_weight").optional(true),

			check("progear_name").optional(true),
			check("is_firearm")
				.optional(true)
				.isIn([true, false])
				.withMessage("Is is firearm value is not valid."),
			check("firmarm_serial_number").optional(true),
			check("isManualLabel")
				.optional(true)
				.isIn([true, false])
				.withMessage("Is ManualLabel value is not valid."),
			check("label_no").optional(true),
			check("lot_no").optional(true),
			check("color").optional(true),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
			check("deleteTag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Delete tags must be an array."),
		],

		removeItem: [
			check("inventory_id")
				.exists()
				.withMessage("Please enter shipment inventory id.")
				.notEmpty()
				.withMessage("Shipment inventory id cannot be blank.")
				.isInt()
				.withMessage("Shipment inventory id only accept numeric value."),
		],

		getJobSummary: [
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
		],

		getUnitByJobId: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
		],

		addSignature: [
			check("job_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job id cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("stage")
				.exists()
				.withMessage("Please enter job stage.")
				.notEmpty()
				.withMessage("Job stage cannot be blank."),
			check("current_job_stage")
				.exists()
				.withMessage("Please enter current job stage")
				.notEmpty()
				.withMessage("Current job stage cannot be blank."),
			check("supervisor_signature_require_note_by_user")
				.optional(true)
				.isString()
				.withMessage("Invalid type, note must be in a string."),
			check("customer_signature_require_note_by_user")
				.optional(true)
				.isString()
				.withMessage("Invalid type, note must be in a string."),
			check("assign_storage_units_to_items")
				.exists()
				.withMessage("Please enter assign_storage_units_to_items.")
		],
		scanItem: [
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("inventoryId")
				.exists()
				.withMessage("Please enter inventory id.")
				.notEmpty()
				.withMessage("Inventory cannot be blank.")
				.isInt()
				.withMessage("Inventory id only accept numeric value."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("staff_id")
				.exists()
				.withMessage("Please enter Staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],
		bulkscanitemsValidation: [
			check("itemArray")
				.exists()
				.withMessage("Please enter itemArray.")
				.isArray()
				.withMessage("itemArray must be an array."),
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("shipment_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("staff_id")
				.exists()
				.withMessage("Please enter Staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],

		bulkAdditionalItemsScanValidation: [
			check("itemArray")
				.exists()
				.withMessage("Please enter itemArray.")
				.isArray()
				.withMessage("itemArray must be an array."),
			check("shipment_id")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("isSelectAllItems")
				.exists()
				.withMessage("Please enter is select all item status.")
				.isIn([true, false])
				.withMessage("Is select all item value is not valid."),
			check("isAddItemsToInventoryScan")
				.exists()
				.withMessage("Please enter is add items to inventory scan status.")
				.isIn([true, false])
				.withMessage("Add items to inventory scan status value is not valid."),
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
		],

		scanItemRandomNumber: [
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("random_number")
				.exists()
				.withMessage("Please enter random number.")
				.notEmpty()
				.withMessage("Random number cannot be blank.")
				.isString()
				.trim()
				.withMessage(`Random number only accept string value.`)
				.matches(/^[a-zA-Z0-9 ]*$/)
				.withMessage(`Random number can only contain alphanumeric.`),
			check("shipmentId")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("staff_id")
				.exists()
				.withMessage("Please enter Staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],
		scanState: [
			check("stageId")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
		],
		overrideScanState: [
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stage id.")
				.notEmpty()
				.withMessage("Stage id cannot be blank.")
				.isInt()
				.withMessage("Stage id only accept numeric value."),
			check("staff_id")
				.exists()
				.withMessage("Please enter Staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
			check("reason")
				.exists()
				.withMessage("Please enter reason for overriding item.")
				.notEmpty()
				.withMessage("Overriding reason cannot be blank.")
				.isString()
				.trim()
				.withMessage("Overriding reason only accept string value."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter job id.")
				.notEmpty()
				.withMessage("Job stage cannot be blank.")
				.isInt()
				.withMessage("Job id only accept numeric value."),
			check("inventoryId")
				.exists()
				.withMessage("Please enter inventory id.")
				.notEmpty()
				.withMessage("Inventory cannot be blank.")
				.isInt()
				.withMessage("Inventory only accept numeric value."),
		],
		addShipmentInventoryMedia: [
			check("inventory_id")
				.exists()
				.withMessage("Please enter inventory id.")
				.notEmpty()
				.withMessage("Inventory stage cannot be blank.")
				.isInt()
				.withMessage("Inventory id only accept numeric value."),
			check("current_job_stage")
				.exists()
				.withMessage("Please enter stageId.")
				.notEmpty()
				.withMessage("stageId cannot be blank.")
				.isInt()
				.withMessage("stageId only accept numeric value."),
		],
	},
	admin: {
		scriptShipmentController: [
			check("shipment_name")
				.exists()
				.withMessage("Please enter shipment name."),
			check("qty")
				.exists()
				.withMessage("Please enter quantity."),
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id."),
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id."),
			check("company_id")
				.exists()
				.withMessage("Please enter company id."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
		],

		scriptAddItemController: [
			check("job_id")
				.exists()
				.withMessage("Please enter job id."),
			check("room_id")
				.exists()
				.withMessage("Please enter room id."),
			check("qty")
				.exists()
				.withMessage("Please enter quantity."),
			check("qr_id")
				.exists()
				.withMessage("Please enter qr id"),
		],

		adminSignIn: [
			check("company_id")
				.optional(true),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("Please enter password.")
				.isLength({ min: 6 })
				.withMessage("Password must be atleast 6 characters long."),
		],

		adminSignInWithCompanyId: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],


		viewEmail: [
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
		],

		superAdminCompanyUpdatePassword: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("new_password")
				.exists()
				.withMessage("New password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("New password field must be atleast 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
			check("confirm_password")
				.exists()
				.withMessage("Confirm password field cannot be blank.")
				.trim()
				.custom((value, { req }) => {
					if (value !== req.body.new_password) {
						return false;
					}
					return true;
				})
				.withMessage("Password confirmation does not match."),
		],
		resetPassword: [
			check("code")
				.exists()
				.withMessage("Please enter code.")
				.trim()
				.notEmpty()
				.withMessage("Verification code cannot be blank.")
				.isLength({ min: 6, max: 6 })
				.withMessage("Verification code must be exact 6 digits."),
			check("new_password")
				.exists()
				.withMessage("New password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("New password field must be atleast 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
			check("confirm_password")
				.exists()
				.withMessage("Confirm password field cannot be blank.")
				.trim()
				.custom((value, { req }) => {
					if (value !== req.body.new_password) {
						return false;
					}
					return true;
				})
				.withMessage("Password confirmation does not match."),
			check("adminId")
				.optional(true),
			check("companyId")
				.optional(true),
			check("staffId")
				.optional(true)
		],

		changePassword: [
			check("old_password")
				.exists()
				.withMessage("Please enter password.")
				.trim()
				.isLength({ min: 8 })
				.withMessage("Password must be at least 8 characters long."),
			check("new_password")
				.exists()
				.withMessage("Please enter new password.")
				.isLength({ min: 8 })
				.withMessage("New password must be at least 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"New Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
			check('confirm_password').exists().withMessage('Please enter confirm password.').trim()
				.custom((value, { req }) => {
					if (value !== req.body.new_password) {
						return false;
					}
					return true;
				}).withMessage('Confirm Password does not match with password.')
		],


		addCompany: [
			check("company_name").exists().withMessage("Please enter company name."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Notes only accepts string value."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("company_identity").exists().withMessage("Please enter company identity."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
		],
		getCompanyList: [
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],

		getCustomerList: [
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],

		changeCompanyStatus: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],

		changeCompanyStatusStorage: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank."),
			check("isDeleted")
				.exists()
				.withMessage("Please enter isDeleted.")
				.isIn([true, false])
				.withMessage("Is Deleted value is not valid."),
			check("isActive")
				.exists()
				.withMessage("Please enter isActive.")
				.isIn([0, 1])
				.withMessage("Is Active value is not valid."),
		],

		viewCompany: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],
		companyIdToken: [
			check("companyIdTokenMoverInventory")
				.exists()
				.withMessage("Please enter companyIdToken.")
				.notEmpty()
				.withMessage("companyIdToken cannot be blank.")
		],
		viewCompanyStorage: [
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
		],
		viewCompanyGet: [
			param("companyId")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],
		viewTag: [
			check("tagId")
				.exists()
				.withMessage("Please enter tag id.")
				.notEmpty()
				.withMessage("Tag id cannot be blank.")
				.isInt()
				.withMessage("Tag id only accept numeric value."),
		],
		viewRoom: [
			param("roomId")
				.exists()
				.withMessage("Please enter room id.")
				.notEmpty()
				.withMessage("Room id cannot be blank.")
				.isInt()
				.withMessage("Room id only accept numeric value."),
		],
		viewItem: [
			param("itemId")
				.exists()
				.withMessage("Please enter item id.")
				.notEmpty()
				.withMessage("Item id cannot be blank.")
				.isInt()
				.withMessage("Item id only accept numeric value."),
		],
		viewGroup: [
			param("groupId")
				.exists()
				.withMessage("Please enter group id.")
				.notEmpty()
				.withMessage("Group id cannot be blank.")
				.isInt()
				.withMessage("Group id only accept numeric value."),
		],
		viewGroupUser: [
			param("groupUserId")
				.exists()
				.withMessage("Please enter groupUser id.")
				.notEmpty()
				.withMessage("GroupUser id cannot be blank.")
				.isInt()
				.withMessage("GroupUser id only accept numeric value."),
		],
		batchRoomList: [
			check("roomList")
				.exists()
				.withMessage("Please enter room list.")
				.isArray()
				.withMessage("Room list must be an array."),
		],
		batchItemList: [
			check("itemList")
				.exists()
				.withMessage("Please enter item list.")
				.isArray()
				.withMessage("Item list must be an array."),
		],
		roomName: [
			check("room_name")
				.exists()
				.withMessage("Please enter room name.")
				.notEmpty()
				.withMessage("Room name cannot be blank.")
				.isString()
				.withMessage("Room name only accept string value."),
			check("admin_id")
				.optional(true),
			check("company_id")
				.optional(true),
			check("staff_id")
				.optional(true),
		],

		updateRoomIsoCodesFromExcel: [
			// No body validation needed as we're using file upload
			// File validation will be handled in the controller
		],
		changeShipmentRoomStatus: [
			check("shipment_room_id")
				.exists()
				.withMessage("Please enter shipment room id.")
				.notEmpty()
				.withMessage("Shipment room id cannot be blank.")
				.isInt()
				.withMessage("Shipment room id only accept numeric value."),
		],
		tagAdd: [
			check("name")
				.exists()
				.withMessage("Please enter tag name.")
				.notEmpty()
				.withMessage("Tag name cannot be blank.")
				.isString()
				.withMessage("Tag name only accept string value."),
			check("tag_for")
				.exists()
				.withMessage("Please enter tag for.")
				.notEmpty()
				.withMessage("Tag for cannot be blank.")
				.isIn(["CUSTOMER", "SHIPMENT", "ITEM"])
				.withMessage("Tag for can only be one of CUSTOMER, SHIPMENT and ITEM."),
			check("color")
				.exists()
				.withMessage("Please enter tag color.")
				.notEmpty()
				.withMessage("Tag color cannot be blank.")
				.isHexColor()
				.withMessage("Tag color should be valid HEX color."),
			check("admin_id")
				.optional(true),
			check("company_id")
				.optional(true),
			check("staff_id")
				.optional(true),

		],
		tagEdit: [
			check("name")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Tag name cannot be blank.")
				.isString()
				.withMessage("Tag name only accept string value."),
			check("tag_for")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Tag for cannot be blank.")
				.isIn(["CUSTOMER", "SHIPMENT", "ITEM"])
				.withMessage("Tag for can only be one of CUSTOMER, SHIPMENT and ITEM."),
			check("color")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Tag color cannot be blank.")
				.isHexColor()
				.withMessage("Tag color should be valid HEX color."),
		],
		itemName: [
			check("item_name")
				.exists()
				.withMessage("Please enter item name.")
				.notEmpty()
				.withMessage("Item name cannot be blank.")
				.isString()
				.withMessage("Item name only accept string value."),
			check("item_volume")
				.optional(true),
			// .isInt()
			// .withMessage("Item weight only accept numeric & greater than 0 value!."),
			check("item_weight")
				.optional(true),
			// .isInt()
			// .withMessage("Item weight only accept numeric & greater than 0 value!."),
			check("admin_id")
				.optional(true),
			check("company_id")
				.optional(true),
			check("staff_id")
				.optional(true),
		],
		groupName: [
			check("name")
				.exists()
				.withMessage("Please enter group name.")
				.notEmpty()
				.withMessage("Group name cannot be blank.")
				.isString()
				.withMessage("Group name only accept string value."),
			check("admin_id")
				.optional(true),
			check("company_id")
				.optional(true),
			check("staff_id")
				.optional(true),
		],
		changeItemStatus: [
			check("item_id")
				.exists()
				.withMessage("Please enter item id.")
				.notEmpty()
				.withMessage("Item id cannot be blank.")
				.isInt()
				.withMessage("Item id only accept numeric value."),
		],
		editCompany: [
			check("company_name")
				.exists()
				.withMessage("Please enter company name.")
				.notEmpty()
				.withMessage("Company name. cannot be blank."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Notes only accepts string value."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("company_identity")
				.exists()
				.withMessage("Please enter company identity.")
				.notEmpty()
				.withMessage("Company identity. cannot be blank."),
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),

		],
		editCompanyStorage: [
			check("company_name")
				.exists()
				.withMessage("Please enter company name.")
				.notEmpty()
				.withMessage("Company name. cannot be blank."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Notes only accepts string value."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("company_identity")
				.exists()
				.withMessage("Please enter company identity.")
				.notEmpty()
				.withMessage("Company identity. cannot be blank."),
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),

		],
		deleteCompany: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],

		addCustomer: [
			// check("customer_name")
			//   .exists()
			//   .withMessage("Please enter customer name."),
			check("first_name").exists().trim().notEmpty().withMessage("Please enter first name."),
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],
		alreadyCustomeCheckStorage: [
			check("email")
				.isArray()
				.withMessage("email must be an array.")
		],
		alreadyCustomeCheckStorageEdit: [
			check("email")
				.isArray()
				.withMessage("email must be an array."),
			check("storage_customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank."),
		],
		addCustomerStorage: [
			check("first_name").exists().trim().notEmpty().withMessage("Please enter first name."),
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("storage_customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],

		addStaff: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("first_name").exists().withMessage("Please enter first name."),
			check("last_name").exists().withMessage("Please enter last name."),
			check("user_notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter user notes."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("phone")
				.optional({ checkFalsy: true })
				.isString()
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array.")
		],

		superAdminStaff: [
			check("company_name").exists().withMessage("Please enter company name."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Notes only accepts string value."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("company_identity").exists().withMessage("Please enter company identity."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("New password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("New password field must be atleast 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
		],
		addStaffStorage: [
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("storage_staff_id")
				.exists()
				.withMessage("Please enter storage staffId id.")
				.notEmpty()
				.withMessage("storage StaffId cannot be blank."),
			check("password")
				.exists()
				.withMessage("Please enter password.")
				.notEmpty()
				.withMessage("Password cannot be blank."),
			check("first_name").exists().withMessage("Please enter first name."),
			check("last_name").exists().withMessage("Please enter last name."),
			check("user_notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter user notes."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("phone")
				.optional({ checkFalsy: true })
				.isString()
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
		],
		editCustomer: [
			check("email")
				.exists()
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("customer_id")
				.exists()
				.trim()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank.")
				.isInt()
				.withMessage("Customer id only accept numeric value."),
			check("first_name")
				.exists()
				.trim()
				.isString()
				.withMessage("Please enter first name.")
				.notEmpty()
				.withMessage("first name cannot be blank."),
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),

			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],
		editCustomerStorage: [
			check("email")
				.exists()
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("storage_customer_id")
				.exists()
				.trim()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank."),
			check("first_name")
				.exists()
				.trim()
				.isString()
				.withMessage("Please enter first name.")
				.notEmpty()
				.withMessage("first name cannot be blank."),
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],
		editStaff: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
			check("company_id")
				.exists()
				.withMessage("Please enter company Id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
			check("first_name")
				.exists()
				.isString()
				.withMessage("Please enter first name.")
				.notEmpty()
				.withMessage("First name cannot be blank."),
			check("last_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter last name."),
			check("user_notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter user notes."),
			check("phone")
				.optional({ checkFalsy: true })
				.isString()
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("email")
				.exists()
				.trim()
				.isEmail()
				.withMessage("Please enter valid email.")
				.notEmpty()
				.withMessage("Email cannot be blank."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],

		editStaffStorage: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank."),
			check("storage_company_id")
				.exists()
				.withMessage("Please enter company Id.")
				.notEmpty()
				.withMessage("Company id cannot be blank."),
			check("first_name")
				.exists()
				.isString()
				.withMessage("Please enter first name.")
				.notEmpty()
				.withMessage("First name cannot be blank."),
			check("last_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter last name."),
			check("user_notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter user notes."),
			check("phone")
				.optional({ checkFalsy: true })
				.isString()
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("email")
				.exists()
				.trim()
				.isEmail()
				.withMessage("Please enter valid email.")
				.notEmpty()
				.withMessage("Email cannot be blank."),

		],

		deleteCustomer: [
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("customer id cannot be blank.")
				.isInt()
				.withMessage("customer id only accept numeric value."),
		],
		changeCustomerStatus: [
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank.")
				.isInt()
				.withMessage("Customer id only accept numeric value."),
		],
		changeCustomerStatusStorage: [
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank."),
			check("isDeleted")
				.exists()
				.withMessage("Please enter isDeleted.")
				.isIn([true, false])
				.withMessage("Is Deleted value is not valid."),
			check("isActive")
				.exists()
				.withMessage("Please enter isActive.")
				.isIn([0, 1])
				.withMessage("Is Active value is not valid."),
		],
		getStaffList: [
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderTable")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Table must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],

		viewCustomer: [
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank.")
				.isInt()
				.withMessage("Customer id only accept numeric value."),
		],

		viewCustomerStorage: [
			check("storage_customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank."),
		],


		viewCustomerEmail: [
			check("email")
				.exists()
				.withMessage("Please enter email address.")
				.trim()
				.isString()
				.withMessage("email address should contain alphanumeric characters only.")
				.isEmail()
				.withMessage("Please enter valid email."),
		],

		viewStaff: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],
		changeStaffStatus: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],

		changeStaffStatusStorage: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank."),
			check("isDeleted")
				.exists()
				.withMessage("Please enter isDeleted.")
				.isIn([true, false])
				.withMessage("Is Deleted value is not valid."),
			check("isActive")
				.exists()
				.withMessage("Please enter isActive.")
				.isIn([0, 1])
				.withMessage("Is Active value is not valid."),
		],

		deleteStaff: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),
		],
		addShipmentType: [
			check("name").exists().withMessage("Please enter name."),
			check("number_of_stages")
				.exists()
				.withMessage("Please enter number of stage.")
				.notEmpty()
				.withMessage("Number of stages cannot be blank.")
				.isInt()
				.withMessage("Number of stages only accept numeric value."),
			check("admin_id")
				.optional(true),
			check("company_id")
				.optional(true),
			check("staff_id")
				.optional(true),

		],
		addShipmentTypeStage: [
			check("name").exists().withMessage("Please enter name."),
		],

		editShipmentType: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
			check("name").exists().withMessage("Please enter name."),
			check("number_of_stages")
				.exists()
				.withMessage("Please enter number of stage.")
				.notEmpty()
				.withMessage("Number of stages cannot be blank.")
				.isInt()
				.withMessage("Number of stages only accept numeric value."),
		],
		statusShipmentType: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
		],
		deleteShipmentType: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
		],

		listShipmentTypeStage: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderTable")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Table must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],

		changeShipmentStageStatus: [
			check("shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
		],

		viewShipmentType: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
		],

		webhookShipment: [
			param("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("ShipmentId cannot be blank.")
		],

		webhookUnit: [
			param("unitId")
				.exists()
				.withMessage("Please enter unitId.")
				.notEmpty()
				.withMessage("UnitId cannot be blank.")
		],

		viewShipmentStage: [
			check("shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
		],

		viewShipmentForShipmentStage: [
			check("local_shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
		],

		editShipmentStage: [
			check("shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
			check("name").exists().withMessage("Please enter name."),
			check("order_of_stages")
				.exists()
				.withMessage("Please enter order of stage.")
				.notEmpty()
				.withMessage("Order of stages cannot be blank.")
				.isInt()
				.withMessage("Order of stages only accept numeric value."),
		],
		editShipmentStageForShipment: [
			check("local_shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
			check("name").exists().withMessage("Please enter name."),
			check("order_of_stages")
				.exists()
				.withMessage("Please enter order of stage.")
				.notEmpty()
				.withMessage("Order of stages cannot be blank.")
				.isInt()
				.withMessage("Order of stages only accept numeric value."),
		],
		qrCodeList: [
			check("jobId")
				.exists()
				.withMessage("please enter the jobId")
				.notEmpty()
				.withMessage("jobId cannot be blank.")
				.isInt()
				.withMessage("jobId only accepts numeric value."),
			check("place")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Place cannot be blank.")
				.isString()
				.withMessage("Place only accepts string value.")
				// .isLength({ max: 31 })
				.withMessage(INCORRECT_LENGTH_PDF_HEADER),
		],
		singalQrCodeList: [
			param("qrId")
				.exists()
				.withMessage("please enter the qrId")
				.notEmpty()
				.withMessage("qrId cannot be blank.")
				.isInt()
				.withMessage("qrId only accepts numeric value."),
		],
		qrCodeCompanyList: [
			check("companyId")
				.exists()
				.withMessage("please enter the companyId")
				.notEmpty()
				.withMessage("companyId cannot be blank.")
				.isInt()
				.withMessage("companyId only accepts numeric value."),
			check("place")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Place cannot be blank.")
				.isString()
				.withMessage("Place only accepts string value.")
				// .isLength({ max: 31 })
				.withMessage(INCORRECT_LENGTH_PDF_HEADER),
		],

		labelGenerateList: [
			check("place")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Place cannot be blank.")
				.isString()
				.withMessage("Place only accepts string value.")
				// .isLength({ max: 31 })
				.withMessage(INCORRECT_LENGTH_PDF_HEADER),
		],

		qrCodeDelete: [
			check("qrCodeId")
				.exists()
				.withMessage("please enter the Qr Id")
				.notEmpty()
				.withMessage("Qr Id cannot be blank.")
				.isInt()
				.withMessage("Qr Id only accepts numeric value."),
		],

		genericLabelDelete: [
			check("qrCodeId")
				.exists()
				.withMessage("please enter the Qr Id")
				.notEmpty()
				.withMessage("Qr Id cannot be blank.")
				.isInt()
				.withMessage("Qr Id only accepts numeric value."),
		],

		genericLabelGenerate: [
			check("quantity")
				.exists()
				.withMessage("please enter the quantity")
				.notEmpty()
				.withMessage("Qr code quantity cannot be blank.")
				.isInt()
				.withMessage("Quantity  only accepts numeric value.")
				.isInt({ mt: 10, lt: 5000 }) // TODO change arbitrary limit to solid
				.withMessage("Quantity should under 5000."),
			check("fromNumber")
				.exists()
				.withMessage("Please enter the from sequence number")
				.notEmpty()
				.withMessage("From sequence number cannot be blank.")
				.isInt()
				.withMessage("From sequence number only accepts numeric value."),
			check("type")
				.exists()
				.withMessage("please enter the type")
				.notEmpty()
				.withMessage("Type cannot be blank.")
				.isInt()
				.withMessage("Type only accepts numeric value.")
				.isInt({ mt: 1, lt: 3 })
				.withMessage("Type should be from of range 1 - 3."),
		],

		qrCodeGenerate: [
			check("jobId")
				.exists()
				.withMessage("please enter the jobId")
				.notEmpty()
				.withMessage("jobId cannot be blank.")
				.isInt()
				.withMessage("jobId only accepts numeric value."),

			check("quantity")
				.exists()
				.withMessage("please enter the quantity")
				.notEmpty()
				.withMessage("Qr code quantity cannot be blank.")
				.isInt()
				.withMessage("Quantity  only accepts numeric value.")
				.isInt({ mt: 1, lt: 1000 }) // TODO change arbitrary limit to solid
				.withMessage("Quantity should under 1000."),

			check("type")
				.exists()
				.withMessage("please enter the type")
				.notEmpty()
				.withMessage("Type cannot be blank.")
				.isInt()
				.withMessage("Type only accepts numeric value.")
				.isInt({ mt: 1, lt: 3 })
				.withMessage("Type should be from of range 1 - 3."),
		],
		staffBasic: [
			check("staff_type")
				.exists()
				.withMessage("Please provide staff type parameter.")
				.notEmpty()
				.withMessage("staff type parameter cannot be blank.")
				.isInt()
				.withMessage("staff type only accept numeric value.")
				.isInt({ mt: 1, lt: 3 })
				.withMessage("staff type should be from of range 1 - 3."),
		],
		shipmentCreate: [
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
			check("shipment_name")
				.exists()
				.withMessage("Please enter shipment name.")
				.notEmpty()
				.withMessage("Shipment name cannot be blank.")
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("email")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Email cannot be blank.")
				.trim()
				.isEmail()
				.withMessage("Email 1 is not in valid format."),
			check("pickup_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("pickup_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("pickup_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("pickup_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("delivery_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_city")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter city."),
			check("delivery_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("delivery_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("delivery_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("external_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("External Reference only accepts string value."),
			check("warehouse")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("external_reference_2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 2 cannot be blank.")
				.isString()
				.withMessage("External Reference 2 only accepts string value."),
			check("external_reference_3")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 3 cannot be blank.")
				.isString()
				.withMessage("External Reference 3 only accepts string value."),
			check("contact_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Contact Reference only accepts string value."),
			check("account_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Account Reference only accepts string value."),
			check("opportunity_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Opportunity Reference only accepts string value."),
			check("move_coordinator")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Move Coordinator only accepts string value."),
			check("wo_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("WO Reference only accepts string value."),
			check("source")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Source only accepts string value."),
			check("pickup_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Pickup Date Estimated cannot be blank."),

			check("delivery_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Delivery Date Estimated cannot be blank.")
				.custom((delivery_date, { req }) => {
					let pickup_date = moment(req.body.pickup_date, "YYYY-MM-DD");
					let start_date = moment(delivery_date, "YYYY-MM-DD");
					start_date.format("YYYY-MM-DD");
					let checkDate = start_date.isValid();
					let isFutureDate = start_date.isSameOrAfter(pickup_date);
					return checkDate && isFutureDate;
				})
				.withMessage("Delivery Date cannot be before pickup."),
			check("estimated_weight")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Vol/Weight cannot be blank.")
				.isFloat()
				.withMessage("Estimated Vol/Weight only accepts numeric value."),
			check("estimated_volume")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Volume cannot be blank.")
				.isFloat()
				.withMessage("Estimated Volume only accepts numeric value."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Work Order Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Work Order Notes only accepts string value."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],
		shipmentCreateStorage: [
			check("storage_shipment_job_id")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank."),
			check("warehouseId")
				.exists()
				.withMessage("Please enter warehouseId.")
				.notEmpty()
				.withMessage("warehouse id cannot be blank."),
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
			check("shipment_name")
				.exists()
				.withMessage("Please enter shipment name.")
				.notEmpty()
				.withMessage("Shipment name cannot be blank.")
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("email")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Email cannot be blank.")
				.trim()
				.isEmail()
				.withMessage("Email 1 is not in valid format."),
			check("pickup_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("pickup_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("pickup_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("pickup_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("delivery_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_city")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter city."),
			check("delivery_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("delivery_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("delivery_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("external_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("External Reference only accepts string value."),
			check("warehouse")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("external_reference_2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 2 cannot be blank.")
				.isString()
				.withMessage("External Reference 2 only accepts string value."),
			check("external_reference_3")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 3 cannot be blank.")
				.isString()
				.withMessage("External Reference 3 only accepts string value."),
			check("contact_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Contact Reference only accepts string value."),
			check("account_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Account Reference only accepts string value."),
			check("opportunity_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Opportunity Reference only accepts string value."),
			check("move_coordinator")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Move Coordinator only accepts string value."),
			check("wo_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("WO Reference only accepts string value."),
			check("source")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Source only accepts string value."),
			check("pickup_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Pickup Date Estimated cannot be blank."),
			check("delivery_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Delivery Date Estimated cannot be blank.")
				.matches(
					/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/
				)
				.withMessage("Delivery Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss).")
				.custom((delivery_date, { req }) => {
					let pickup_date = moment(req.body.pickup_date, "YYYY-MM-DD HH:mm:ss");
					let start_date = moment(delivery_date, "YYYY-MM-DD HH:mm:ss");
					start_date.format("YYYY-MM-DD HH:mm:ss");
					let checkDate = start_date.isValid();
					let isFutureDate = start_date.isSameOrAfter(pickup_date);
					return checkDate && isFutureDate;
				})
				.withMessage("Delivery Date cannot be before pickup."),
			check("estimated_weight")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Vol/Weight cannot be blank.")
				.isFloat()
				.withMessage("Estimated Vol/Weight only accepts numeric value."),
			check("estimated_volume")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Volume cannot be blank.")
				.isFloat()
				.withMessage("Estimated Volume only accepts numeric value."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Work Order Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Work Order Notes only accepts string value."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],
		shipmentCheck: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
		],

		shipmentUpdate: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("shipment_type_id").not().exists().withMessage("Shipment type is not alterable."),
			check("warehouse")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("email")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Email cannot be blank.")
				.trim()
				.isEmail()
				.withMessage("Email 1 is not in valid format."),
			check("pickup_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("delivery_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("external_reference")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference cannot be blank.")
				.isString()
				.trim()
				.withMessage("External Reference only accepts string value."),
			check("external_reference_2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 2 cannot be blank.")
				.isString()
				.withMessage("External Reference 2 only accepts string value."),
			check("external_reference_3")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 3 cannot be blank.")
				.isString()
				.withMessage("External Reference 3 only accepts string value."),
			check("pickup_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Pickup Date Estimated cannot be blank.")
				.matches(dateRegex)
				.withMessage("Pickup Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss)."),
			check("delivery_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Delivery Date Estimated cannot be blank.")
				.trim()
				.matches(dateRegex)
				.withMessage("Delivery Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss)."),
			check("estimated_weight")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Vol/Weight cannot be blank.")
				.isFloat()
				.withMessage("Estimated Vol/Weight only accepts numeric value."),
			check("estimated_volume")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated volume cannot be blank.")
				.isInt()
				.withMessage("Estimated volume only accepts numeric value."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Work Order Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Work Order Notes only accepts string value."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],
		shipmentFetchDetail: [
			check("shipmentId")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
		],
		inventoryDetail: [
			check("inventoryId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
		],

		addCommentToInventory: [
			check("inventoryId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("comment")
				.exists()
				.withMessage("Please enter comment")
				.notEmpty()
				.withMessage("Comment cannot be blank.")
				.isString()
				.withMessage("Comment only accept string value."),
		],

		addNoteToInventory: [
			check("inventoryId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("note")
				.exists()
				.withMessage("Please enter note")
				.notEmpty()
				.withMessage("Note cannot be blank.")
				.isString()
				.withMessage("Note only accept string value."),
		],

		forceStageChange: [
			check("altered_stage_id")
				.exists()
				.withMessage("Please enter new stage.")
				.notEmpty()
				.withMessage("New stage cannot be blank,")
				.isInt()
				.withMessage(" Please select new stage."),
			check("reason")
				.exists()
				.withMessage("Please enter valid reason for stage change.")
				.notEmpty()
				.withMessage("Reason for stage change cannot be blank.")
				.isString()
				.withMessage("Reason accept only numeric value."),
		],

		shipmentList: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn([...COMMON_JOB_SHIPMENT_ATTRIBUTES]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		roomList: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		tagListData: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name", "tag_for"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		itemList: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name", "volume", "weight"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),

		],

		groupList: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name", "volume", "weight"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),

		],
		linkWorkers: [
			check("role")
				.exists()
				.withMessage("Please enter role id.")
				.notEmpty()
				.withMessage("role id cannot be blank.")
				.isString()
				.trim()
				.isIn(["supervisor", "worker"])
				.withMessage("role should be either supervisor/worker."),
			check("staff_id")
				.exists()
				.withMessage("Please enter staff ID.")
				.notEmpty()
				.withMessage("staff ID cannot be blank.")
				.isInt()
				.withMessage("staff ID only accept numeric value."),
		],
		unlinkWorkers: [
			check("role")
				.exists()
				.withMessage("Please enter role id.")
				.notEmpty()
				.withMessage("role id cannot be blank.")
				.isString()
				.trim()
				.isIn(["supervisor", "worker"])
				.withMessage("role should be either supervisor/worker."),
			check("staff_worker_id")
				.exists()
				.withMessage("Please enter staff ID.")
				.notEmpty()
				.withMessage("staff ID cannot be blank.")
				.isInt()
				.withMessage("staff ID only accept numeric value."),
		],
		customerId: [
			check("customerId")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("customer id cannot be blank.")
				.isInt()
				.withMessage("customer id only accept numeric value."),
		],
		orderingField: [
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES, "isScanned"])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
		],
		updatePassword: [
			check("staff_id")
				.exists()
				.withMessage("Please enter staff id.")
				.notEmpty()
				.withMessage("Staff id cannot be blank.")
				.isInt()
				.withMessage("Staff id only accept numeric value."),

			check("password")
				.exists()
				.withMessage("Password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("New password field must be atleast 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
		],
		isPassword: [
			check("password")
				.exists()
				.withMessage("Password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("New password field must be atleast 8 characters long.")
				.matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?!.* )(?=.*[^a-zA-Z0-9]).{8,}$/, "i")
				.withMessage(
					"Password must include one lowercase character, one uppercase character, a number, and a special character."
				),
		],
		addApiUser: [
			check("from")
				.exists()
				.withMessage("Please give name of access cms!")
				.notEmpty()
				.withMessage("Please give name of access cms!")
				.trim()
				.withMessage("Please give name of access cms!"),
			check("status")
				.exists()
				.withMessage("Please give api access status")
				.notEmpty()
				.withMessage("Please give api access status"),
			check("type")
				.exists()
				.withMessage("Please select access type!")
				.notEmpty()
				.withMessage("Please select access type!"),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("Password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("Password must be atleast 8 characters long."),
		],
		reloadToken: [
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("password")
				.exists()
				.withMessage("Password field cannot be blank.")
				.isLength({ min: 8 })
				.withMessage("Password must be atleast 8 characters long."),
		],
		sendRequest: [
			check("first_name")
				.exists()
				.notEmpty()
				.withMessage("First name cannot be blank.")
				.isString()
				.trim()
				.withMessage("First name only accepts string value."),
			check("last_name")
				.exists()
				.notEmpty()
				.withMessage("Last name cannot be blank.")
				.isString()
				.trim()
				.withMessage("Last name only accepts string value."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("phone")
				.exists()
				.withMessage("Please enter phone number.")
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("company_name")
				.exists()
				.notEmpty()
				.withMessage("Company name cannot be blank.")
				.isString()
				.trim()
				.withMessage("Company name only accepts string value."),
			check("remark")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Remark name cannot be blank.")
				.isString()
				.trim()
				.withMessage("Remark name only accepts string value."),
		],
	},

	openApi: {
		addCustomer: [
			check("first_name")
				.exists()
				.trim()
				.notEmpty()
				.withMessage("Please enter first name."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],
		shipmentCreate: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id.")
				.notEmpty()
				.withMessage("Shipment type id cannot be blank.")
				.isInt()
				.withMessage("Shipment type id only accept numeric value."),
			check("shipment_name")
				.exists()
				.withMessage("Please enter shipment name.")
				.notEmpty()
				.withMessage("Shipment name cannot be blank.")
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("email")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Email cannot be blank.")
				.trim()
				.isEmail()
				.withMessage("Email 1 is not in valid format."),
			check("pickup_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("pickup_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("pickup_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("pickup_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("delivery_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_city")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter city."),
			check("delivery_state")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter state."),
			check("delivery_zipcode")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter zip-code."),
			check("delivery_country")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter country."),
			check("external_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("External Reference only accepts string value."),
			check("warehouse")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("external_reference_2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 2 cannot be blank.")
				.isString()
				.withMessage("External Reference 2 only accepts string value."),
			check("external_reference_3")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 3 cannot be blank.")
				.isString()
				.withMessage("External Reference 3 only accepts string value."),
			check("contact_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Contact Reference only accepts string value."),
			check("account_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Account Reference only accepts string value."),
			check("opportunity_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Opportunity Reference only accepts string value."),
			check("move_coordinator")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Move Coordinator only accepts string value."),
			check("wo_reference")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("WO Reference only accepts string value."),
			check("source")
				.optional({ checkFalsy: true })
				.isString()
				.trim()
				.withMessage("Source only accepts string value."),
			check("pickup_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Pickup Date Estimated cannot be blank.")
				.custom((pickup_date) => {
					let current_date = moment(new Date(), "YYYY-MM-DD HH:mm:ss");
					let start_date = moment(pickup_date, "YYYY-MM-DD HH:mm:ss");
					start_date.format("YYYY-MM-DD HH:mm:ss");
					let checkDate = start_date.isValid();
					let isFutureDate = start_date.isSameOrAfter(current_date);
					return checkDate && isFutureDate;
				})
				.withMessage("Pickup Date cannot be in past."),
			check("delivery_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Delivery Date Estimated cannot be blank.")
				.matches(
					/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24\:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/
				)
				.withMessage("Delivery Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss).")
				.custom((delivery_date, { req }) => {
					let pickup_date = moment(req.body.pickup_date, "YYYY-MM-DD HH:mm:ss");
					let start_date = moment(delivery_date, "YYYY-MM-DD HH:mm:ss");
					start_date.format("YYYY-MM-DD HH:mm:ss");
					let checkDate = start_date.isValid();
					let isFutureDate = start_date.isSameOrAfter(pickup_date);
					return checkDate && isFutureDate;
				})
				.withMessage("Delivery Date cannot be before pickup."),
			check("estimated_weight")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Vol/Weight cannot be blank.")
				.isFloat()
				.withMessage("Estimated Vol/Weight only accepts numeric value."),
			check("estimated_volume")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Volume cannot be blank.")
				.isFloat()
				.withMessage("Estimated Volume only accepts numeric value."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Work Order Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Work Order Notes only accepts string value."),
		],
		viewCustomer: [
			check("customer_id")
				.exists()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank.")
				.isInt()
				.withMessage("Customer id only accept numeric value."),
		],
		getCustomerList: [
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		shipmentTypeList: [
			check("orderBy")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order By must be in a string."),
			check("orderSequence")
				.optional(true)
				.isString()
				.withMessage("Invalid type, order Sequence must be in a string."),
			check("pageNo")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			check("search").optional(true).trim(),
			check("pageSize")
				.optional(true)
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		tagAdd: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("name")
				.exists()
				.withMessage("Please enter tag name.")
				.notEmpty()
				.withMessage("Tag name cannot be blank.")
				.isString()
				.withMessage("Tag name only accept string value."),
			check("tag_for")
				.exists()
				.withMessage("Please enter tag for.")
				.notEmpty()
				.withMessage("Tag for cannot be blank.")
				.isIn(["CUSTOMER", "SHIPMENT"])
				.withMessage("Tag for can only be one of CUSTOMER or SHIPMENT."),
			check("color")
				.exists()
				.withMessage("Please enter tag color.")
				.notEmpty()
				.withMessage("Tag color cannot be blank.")
				.isHexColor()
				.withMessage("Tag color should be valid HEX color."),
		],
		shipmentList: [
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn([...COMMON_JOB_SHIPMENT_ATTRIBUTES]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		staffList: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		companyKey: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		shipmentFetchDetail: [
			param("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		qrCodeGenerate: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("jobId")
				.exists()
				.withMessage("please enter the jobId")
				.notEmpty()
				.withMessage("jobId cannot be blank.")
				.isInt()
				.withMessage("jobId only accepts numeric value."),
			check("quantity")
				.exists()
				.withMessage("please enter the quantity")
				.notEmpty()
				.withMessage("Qr code quantity cannot be blank.")
				.isInt()
				.withMessage("Quantity  only accepts numeric value.")
				.isInt({ mt: 1, lt: 1000 }) // TODO change arbitrary limit to solid
				.withMessage("Quantity should under 1000."),
			check("type")
				.exists()
				.withMessage("please enter the type")
				.notEmpty()
				.withMessage("Type cannot be blank.")
				.isInt()
				.withMessage("Type only accepts numeric value.")
				.isInt({ mt: 1, lt: 3 })
				.withMessage("Type should be from of range 1 - 3."),
		],
		apiKeyValidators: [
			check("company_id")
				.exists()
				.withMessage("Please enter company id.")
				.notEmpty()
				.withMessage("Company id cannot be blank.")
				.isInt()
				.withMessage("Company id only accept numeric value."),
		],
		linkWorkers: [
			check("role")
				.exists()
				.withMessage("Please enter role id.")
				.notEmpty()
				.withMessage("role id cannot be blank.")
				.isString()
				.trim()
				.isIn(["supervisor", "worker"])
				.withMessage("role should be either supervisor/worker."),
			check("staff_id")
				.exists()
				.withMessage("Please enter staff ID.")
				.notEmpty()
				.withMessage("staff ID cannot be blank.")
				.isInt()
				.withMessage("staff ID only accept numeric value."),
			check("stage_id")
				.exists()
				.withMessage("Please enter stageId.")
				.notEmpty()
				.withMessage("stageId cannot be blank.")
				.isInt()
				.withMessage("stageId only accept numeric value."),
		],
		unlinkWorkers: [
			check("role")
				.exists()
				.withMessage("Please enter role id.")
				.notEmpty()
				.withMessage("role id cannot be blank.")
				.isString()
				.trim()
				.isIn(["supervisor", "worker"])
				.withMessage("role should be either supervisor/worker."),
			check("assignId")
				.exists()
				.withMessage("Please enter assign Id.")
				.notEmpty()
				.withMessage("Assign Id cannot be blank.")
				.isInt()
				.withMessage("Assign Id only accept numeric value."),
		],
		tagListData: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name", "tag_for"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		qrCodeList: [
			param("jobId")
				.exists()
				.withMessage("please enter the jobId")
				.notEmpty()
				.withMessage("jobId cannot be blank.")
				.isInt()
				.withMessage("jobId only accepts numeric value."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		roomName: [
			check("room_name")
				.exists()
				.withMessage("Please enter room name.")
				.notEmpty()
				.withMessage("Room name cannot be blank.")
				.isString()
				.withMessage("Room name only accept string value."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		itemName: [
			check("item_name")
				.exists()
				.withMessage("Please enter item name.")
				.notEmpty()
				.withMessage("Item name cannot be blank.")
				.isString()
				.withMessage("Item name only accept string value."),
			check("item_volume")
				.optional(true),
			check("item_weight")
				.optional(true),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		shipmentUpdate: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("shipment_type_id").not().exists().withMessage("Shipment type is not alterable."),
			check("warehouse")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Shipment name only accept numeric value."),
			check("email")
				.exists()
				.withMessage("Email cannot be blank.")
				.trim()
				.isEmail()
				.withMessage("Email is not in valid format."),
			check("pickup_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("pickup_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Origin Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Origin Address only accepts string value."),
			check("delivery_address")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("delivery_address2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Destination Address cannot be blank.")
				.isString()
				.trim()
				.withMessage("Destination Address only accepts string value."),
			check("external_reference")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference cannot be blank.")
				.isString()
				.trim()
				.withMessage("External Reference only accepts string value."),
			check("external_reference_2")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 2 cannot be blank.")
				.isString()
				.withMessage("External Reference 2 only accepts string value."),
			check("external_reference_3")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("External Reference 3 cannot be blank.")
				.isString()
				.withMessage("External Reference 3 only accepts string value."),
			check("pickup_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Pickup Date Estimated cannot be blank.")
				.matches(dateRegex)
				.withMessage("Pickup Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss)."),
			check("delivery_date")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Delivery Date Estimated cannot be blank.")
				.trim()
				.matches(dateRegex)
				.withMessage("Delivery Date is in incorrect format allowed (YYYY-MM-DD HH:mm:ss)."),
			check("estimated_weight")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated Vol/Weight cannot be blank.")
				.isFloat()
				.withMessage("Estimated Vol/Weight only accepts numeric value."),
			check("estimated_volume")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Estimated volume cannot be blank.")
				.isInt()
				.withMessage("Estimated volume only accepts numeric value."),
			check("notes")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("Work Order Notes cannot be blank.")
				.isString()
				.trim()
				.withMessage("Work Order Notes only accepts string value."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
		],
		editCustomer: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("customer_id")
				.exists()
				.trim()
				.withMessage("Please enter customer id.")
				.notEmpty()
				.withMessage("Customer id cannot be blank.")
				.isInt()
				.withMessage("Customer id only accept numeric value."),
			check("email")
				.exists()
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("first_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter first name."),
			check("account_id")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_id."),
			check("account_name")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter account_name."),
			check("address1")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address1."),
			check("address2")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter address2."),
			check("city").optional({ checkFalsy: true }).isString().withMessage("Please enter city."),
			check("state").optional({ checkFalsy: true }).isString().withMessage("Please enter state."),
			check("zipCode").optional({ checkFalsy: true }).isString().withMessage("Please enter zip-code."),
			check("country").optional({ checkFalsy: true }).isString().withMessage("Please enter country."),
			check("phone")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone number must be atleast 6 characters long."),
			check("phone2")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("phone3")
				.optional({ checkFalsy: true })
				.isLength({ min: 6 })
				.withMessage("phone2 number must be atleast 6 characters long."),
			check("tag")
				.optional({ checkFalsy: true })
				.customSanitizer((value) =>
					JSON.parse(value, (key, value) => (value.length === 0 ? null : value))
				)
				.isArray()
				.withMessage("Tags must be an array."),
			check("sales_rep")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter sales_rep."),
			check("notes")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Please enter customer notes."),
			check("email2")
				.optional({ checkFalsy: true })
				.trim()
				.isEmail()
				.withMessage("Please enter valid email2."),
		],
		roomList: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
		],
		itemList: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			query("order_by_fields")
				.optional({ checkFalsy: true })
				.isString()
				.withMessage("Order by only accept string value.")
				.trim()
				.isIn(["name", "volume", "weight"]),
			query("order_sequence")
				.optional({ checkFalsy: true })
				.isIn(["DESC", "ASC"])
				.withMessage("Invalid type, order Sequence must be correct."),
			query("page_no")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page no must be in a integer."),
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),

		],

		addShipmentType: [
			check("name").exists().withMessage("Please enter name."),
			check("number_of_stages")
				.exists()
				.withMessage("Please enter number of stage.")
				.notEmpty()
				.withMessage("Number of stages cannot be blank.")
				.isInt()
				.withMessage("Number of stages only accept numeric value."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		shipmentDelete: [
			check("shipmentId")
				.exists()
				.withMessage("Please enter shipment id.")
				.notEmpty()
				.withMessage("Shipment id cannot be blank.")
				.isInt()
				.withMessage("Shipment id only accept numeric value."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],

		unitTypeList: [
			check("warehouseId")
				.exists()
				.withMessage("Please enter warehouseId.")
				.notEmpty()
				.withMessage("warehouse id cannot be blank."),
		],

		allItemsListValidation: [
			query("search")
				.optional({ checkFalsy: true })
				.trim()
				.isString()
				.withMessage("Search field is not valid"),
			query("page_size")
				.optional({ checkFalsy: true })
				.isInt()
				.withMessage("Invalid type, page size must be in a integer."),
			check("orderingField")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn([...COMMON_INVENTORY_ATTRIBUTES])
				.withMessage("ordering fields value is incorrect."),
			check("orderingWay")
				.optional({ checkFalsy: true })
				.notEmpty()
				.withMessage("ordering fields cannot be blank.")
				.isIn(["DESC", "ASC"])
				.withMessage("Sorting order value is incorrect."),
			param("shipmentId")
				.exists()
				.withMessage("Please enter shipmentId.")
				.notEmpty()
				.withMessage("ShipmentId cannot be blank.")
		],
		addShipmentTypeStage: [
			check("name")
				.exists()
				.withMessage("Please enter name."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("shipment_job_id")
				.exists()
				.withMessage("Please enter shipment job id.")
				.notEmpty()
				.withMessage("Shipment job id cannot be blank."),
			check("add_items_to_inventory")
				.exists()
				.withMessage("Please enter add items to inventory status.")
				.isIn([true, false])
				.withMessage("Add items to inventory value is not valid."),
			check("assign_storage_units_to_items")
				.exists()
				.withMessage("Please enter assign storage units to items status.")
				.isIn([true, false])
				.withMessage("Assign storage units to items value is not valid."),
			check("unassign_storage_units_from_items")
				.exists()
				.withMessage("Please enter unassign storage units from items status.")
				.isIn([true, false])
				.withMessage("Unassign storage units from items value is not valid."),
			check("remove_items_to_inventory")
				.exists()
				.withMessage("Please enter remove items to inventory status.")
				.isIn([true, false])
				.withMessage("Remove items to inventory value is not valid."),
		],
		editShipmentStageForShipment: [
			check("local_shipment_stage_id")
				.exists()
				.withMessage("Please enter shipment stage id.")
				.notEmpty()
				.withMessage("Shipment stage id cannot be blank.")
				.isInt()
				.withMessage("Shipment stage id only accept numeric value."),
			check("name").exists().withMessage("Please enter name."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		viewShipmentStageForShipment: [
			check("shipment_job_id")
				.exists()
				.withMessage("Please enter shipment job id.")
				.notEmpty()
				.withMessage("Shipment job id cannot be blank."),
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
		],
		shipmentAndCustomerController: [
			check("company_key")
				.exists()
				.withMessage("Please enter company key.")
				.notEmpty()
				.withMessage("Company key cannot be blank."),
			check("email")
				.exists()
				.withMessage("Please enter email.")
				.trim()
				.isEmail()
				.withMessage("Please enter valid email."),
			check("first_name")
				.exists()
				.trim()
				.notEmpty()
				.withMessage("Please enter first name."),
			check("last_name")
				.exists()
				.trim()
				.notEmpty()
				.withMessage("Please enter last name."),
			check("shipment_type_id")
				.exists()
				.withMessage("Please enter shipment type id."),
			check("shipment_name")
				.exists()
				.withMessage("Please enter shipment name."),
			check("is_customer_portal_invite")
				.exists()
				.withMessage("Please enter is customer portal invite status.")
				.isIn([true, false])
				.withMessage("Is customer portal invite value is not valid."),
			check("is_create_label")
				.exists()
				.withMessage("Please enter is create label status.")
				.isIn([true, false])
				.withMessage("Is create label value is not valid."),
		]
	}
};

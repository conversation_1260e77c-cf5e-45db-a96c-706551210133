'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.renameColumn(
                'shipment_job_signatures',
                'signature',
                'supervisor_signature',
                Sequelize.TEXT
            );
            await queryInterface.addColumn(
                'shipment_job_signatures',
                'customer_signature',
                Sequelize.TEXT
            );

            await queryInterface.changeColumn("shipment_job_signatures", 'stage', {
                type: Sequelize.INTEGER(11),
                allowNull: true,
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
                references: {
                    model: "shipment_type_stages",
                    key: "shipment_stage_id",
                }
            });
            return Promise.resolve()

        } catch (e) {
            return Promise.reject(e);
        }
    },
    down: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.renameColumn(
                'shipment_job_signatures',
                'supervisor_signature',
                'signature',
                Sequelize.TEXT
            );
            await queryInterface.removeColumn(
                'shipment_job_signatures',
                'customer_signature',
            );
            await queryInterface.changeColumn(
                "shipment_job_signatures",
                "stage", {
                type: Sequelize.INTEGER,
            })
            return Promise.resolve()

        } catch (e) {
            return Promise.reject(e);
        }
    }
};

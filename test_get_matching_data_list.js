const axios = require('axios');

async function testGetMatchingDataList(options = {}) {
    try {
        console.log("📋 Testing Get Matching Data List API");
        console.log("=====================================");
        
        // API endpoint
        const apiUrl = 'http://localhost:8421/api/admin/item/get-matching-data-list';
        
        // Default options
        const defaultOptions = {
            similarity_threshold: 75,
            include_exact_matches: true,
            include_fuzzy_matches: true,
            sort_by: 'similarity', // 'similarity', 'name', 'suggestion_id'
            sort_order: 'desc'     // 'asc', 'desc'
        };
        
        const requestData = { ...defaultOptions, ...options };
        
        console.log("📡 Making API call to:", apiUrl);
        console.log("📋 Request parameters:");
        console.log(`   - Similarity threshold: ${requestData.similarity_threshold}%`);
        console.log(`   - Include exact matches: ${requestData.include_exact_matches}`);
        console.log(`   - Include fuzzy matches: ${requestData.include_fuzzy_matches}`);
        console.log(`   - Sort by: ${requestData.sort_by}`);
        console.log(`   - Sort order: ${requestData.sort_order}`);
        console.log("\n⏳ Processing...\n");
        
        // Make API call
        const response = await axios.post(apiUrl, requestData, {
            timeout: 120000 // 2 minutes timeout
        });
        
        console.log("✅ API Response Status:", response.status);
        
        if (response.data.status === 1) {
            console.log("🎉 API call successful!");
            console.log("📊 MATCHING DATA RESULTS:");
            console.log("===============================================================");
            
            const data = response.data.data;
            
            // Display summary
            console.log("📈 SUMMARY:");
            console.log("===========");
            console.log(`📋 Total suggestions checked: ${data.summary.total_suggestions_checked}`);
            console.log(`📋 Total ISO codes available: ${data.summary.total_iso_codes_available}`);
            console.log(`✅ Exact matches: ${data.summary.exact_matches_count}`);
            console.log(`🔍 Fuzzy matches: ${data.summary.fuzzy_matches_count}`);
            console.log(`❌ No matches: ${data.summary.no_matches_count}`);
            console.log(`📊 Total matches: ${data.summary.total_matches_count}`);
            console.log(`📈 Match percentage: ${data.summary.match_percentage}`);
            console.log(`🎯 Similarity threshold: ${data.summary.similarity_threshold_used}%`);
            
            // Display exact matches
            if (data.exact_matches && data.exact_matches.length > 0) {
                console.log("\n✅ EXACT MATCHES (100% similarity):");
                console.log("====================================");
                data.exact_matches.slice(0, 10).forEach((match, index) => {
                    console.log(`\n   ${index + 1}. Suggestion ID: ${match.suggestion_id}`);
                    console.log(`      Suggestion Name: "${match.suggestion_name}"`);
                    console.log(`      ISO Code Name: "${match.best_match.iso_code_name}"`);
                    console.log(`      ISO Code: "${match.best_match.iso_code}" (ID: ${match.best_match.iso_code_id})`);
                    console.log(`      Company ID: ${match.company_id}, Status: ${match.status}`);
                    if (match.total_matches_found > 1) {
                        console.log(`      📊 Multiple matches found: ${match.total_matches_found}`);
                    }
                });
                
                if (data.exact_matches.length > 10) {
                    console.log(`\n   ... and ${data.exact_matches.length - 10} more exact matches`);
                }
            }
            
            // Display fuzzy matches
            if (data.fuzzy_matches && data.fuzzy_matches.length > 0) {
                console.log("\n🔍 FUZZY MATCHES (similar but not exact):");
                console.log("==========================================");
                data.fuzzy_matches.slice(0, 10).forEach((match, index) => {
                    console.log(`\n   ${index + 1}. Suggestion ID: ${match.suggestion_id}`);
                    console.log(`      Suggestion Name: "${match.suggestion_name}"`);
                    console.log(`      Best Match: "${match.best_match.iso_code_name}"`);
                    console.log(`      Similarity: ${match.best_match.similarity}%`);
                    console.log(`      ISO Code: "${match.best_match.iso_code}" (ID: ${match.best_match.iso_code_id})`);
                    console.log(`      Company ID: ${match.company_id}, Status: ${match.status}`);
                    
                    // Show all matches if multiple found
                    if (match.total_matches_found > 1) {
                        console.log(`      📊 All ${match.total_matches_found} matches:`);
                        match.all_matches.forEach((altMatch, altIndex) => {
                            console.log(`         ${altIndex + 1}. "${altMatch.iso_code_name}" (${altMatch.similarity}%)`);
                        });
                    }
                });
                
                if (data.fuzzy_matches.length > 10) {
                    console.log(`\n   ... and ${data.fuzzy_matches.length - 10} more fuzzy matches`);
                }
            }
            
            // Display no matches (first few)
            if (data.no_matches && data.no_matches.length > 0) {
                console.log("\n❌ NO MATCHES FOUND:");
                console.log("=====================");
                data.no_matches.slice(0, 5).forEach((noMatch, index) => {
                    console.log(`   ${index + 1}. ID: ${noMatch.suggestion_id}, Name: "${noMatch.suggestion_name}"`);
                    console.log(`      Reason: ${noMatch.reason}`);
                });
                
                if (data.no_matches.length > 5) {
                    console.log(`   ... and ${data.no_matches.length - 5} more items with no matches`);
                }
            }
            
            // Show interesting statistics
            console.log("\n📊 INTERESTING FINDINGS:");
            console.log("=========================");
            
            // Find items with multiple matches
            const multipleMatches = [];
            if (data.exact_matches) {
                multipleMatches.push(...data.exact_matches.filter(m => m.total_matches_found > 1));
            }
            if (data.fuzzy_matches) {
                multipleMatches.push(...data.fuzzy_matches.filter(m => m.total_matches_found > 1));
            }
            
            if (multipleMatches.length > 0) {
                console.log(`🔍 Items with multiple matches: ${multipleMatches.length}`);
                multipleMatches.slice(0, 3).forEach((item, index) => {
                    console.log(`   ${index + 1}. "${item.suggestion_name}" matches ${item.total_matches_found} ISO codes`);
                });
            }
            
            // Find highest similarity fuzzy matches
            if (data.fuzzy_matches && data.fuzzy_matches.length > 0) {
                const topFuzzy = data.fuzzy_matches.slice(0, 3);
                console.log("🎯 Top fuzzy matches:");
                topFuzzy.forEach((item, index) => {
                    console.log(`   ${index + 1}. "${item.suggestion_name}" → "${item.best_match.iso_code_name}" (${item.best_match.similarity}%)`);
                });
            }
            
            console.log(`\n✅ SUCCESS: ${response.data.message}`);
            
        } else {
            console.log("⚠️ API returned error:", response.data.message);
            console.log("📄 Full response:", JSON.stringify(response.data, null, 2));
        }
        
    } catch (error) {
        console.log("\n❌ ERROR OCCURRED:");
        console.log("===============================================");
        
        if (error.response) {
            console.log("📡 API Error Response:");
            console.log("Status:", error.response.status);
            console.log("Message:", error.response.data?.message || 'No message');
            console.log("Full Response:", JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log("🌐 Network Error - No response received");
            console.log("Make sure the server is running on port 8421");
            console.log("Command to start server: npm start or node server.js");
        } else {
            console.log("⚠️ Error:", error.message);
        }
    }
}

// Test different scenarios
async function runTests() {
    console.log("📋 TESTING INSTRUCTIONS:");
    console.log("========================");
    console.log("1. Make sure your server is running: npm start");
    console.log("2. Ensure both item_iso_code and item_suggestion tables have data");
    console.log("3. This API shows all matching data between the tables");
    console.log("4. You can filter and sort the results");
    console.log("5. Authentication is disabled for testing\n");

    // Test 1: Default settings (all matches, sorted by similarity)
    console.log("🚀 Test 1: Get all matching data (default settings)");
    await testGetMatchingDataList();
    
    // Uncomment below to run additional tests
    
    // Test 2: Only exact matches
    // console.log("\n" + "=".repeat(80));
    // console.log("🚀 Test 2: Only exact matches");
    // await testGetMatchingDataList({
    //     include_fuzzy_matches: false,
    //     sort_by: 'name',
    //     sort_order: 'asc'
    // });
    
    // Test 3: Only fuzzy matches with higher threshold
    // console.log("\n" + "=".repeat(80));
    // console.log("🚀 Test 3: Only fuzzy matches (85% threshold)");
    // await testGetMatchingDataList({
    //     similarity_threshold: 85,
    //     include_exact_matches: false,
    //     sort_by: 'similarity',
    //     sort_order: 'desc'
    // });
}

// Run the tests
runTests();

"use strict";
module.exports = (sequelize, DataTypes) => {
	const generic_label = sequelize.define(
		"generic_label",
		{
			qr_code_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			company_id: DataTypes.INTEGER,
			label_number: DataTypes.INTEGER,
			random_number: DataTypes.STRING,
			qr_image: DataTypes.TEXT,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_at: {
				type: DataTypes.DATE,
			},
			updated_at: {
				type: DataTypes.DATE,
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			paranoid: true,
			indexes: [
				{
					unique: true,
					fields: ["random_number"],
				},
			],
		}
	);
	generic_label.associate = function (models) {
		// associations can be defined here
		generic_label.belongsTo(models.company, { foreignKey: "company_id" });
        generic_label.hasMany(models.shipment_inventory, {
			foreignKey: "qr_id",
		});
	};
	return generic_label;
};

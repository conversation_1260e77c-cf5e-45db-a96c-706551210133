{"rules": {"block-no-empty": true, "comment-no-empty": true, "string-no-newline": true, "function-linear-gradient-no-nonstandard-direction": true, "media-feature-name-no-unknown": true, "color-no-invalid-hex": true, "font-family-no-duplicate-names": true, "no-duplicate-at-import-rules": true, "no-empty-source": true, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["/^(mat|md|fa)-/"], "ignore": ["custom-elements"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["value", "at-root", "content", "debug", "each", "else", "error", "for", "function", "if", "include", "mixin", "return", "warn", "while", "extend", "use", "/^@.*/"]}], "no-duplicate-selectors": true, "font-family-no-missing-generic-family-keyword": true, "no-extra-semicolons": true, "no-invalid-double-slash-comments": true, "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["ng-deep", "v-deep"]}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["local", "global", "export", "import"]}], "keyframe-declaration-no-important": true, "property-no-unknown": [true, {"ignoreProperties": ["composes", "/^mso-/"], "ignoreSelectors": ["/^:export.*/", "/^:import.*/"]}], "declaration-block-no-shorthand-property-overrides": true, "unit-no-unknown": [true, {"ignoreUnits": ["x"]}], "function-calc-no-invalid": true}}
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventory_photos',
        'is_thumbnail',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_inventory_photos', 'is_thumbnail');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

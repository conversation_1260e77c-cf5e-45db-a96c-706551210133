const {
    staff
} = require("../../database/schemas");
const { Op, literal, fn, where, col } = require("sequelize");


exports.getStaffList = async (request, body) => {

    const pageNo = request.pageNo ? request.pageNo : 1;
    const pageSize = request.pageSize ? request.pageSize : 10;
    const orderBy = request.orderBy ? request.orderBy : "created_at";
    const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
    const search = request.search ? request.search : "";
    const status = request.filter ? request.filter : "active";
    pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

    return staff.findAndCountAll({
        where:{
            [Op.and]: [
				{ company_id: body.company_id },
				{
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + search + "%" } },
						{ last_name: { [Op.like]: "%" + search + "%" } },
						{ email: { [Op.like]: "%" + search + "%" } },
					],
					is_deleted: "0",
					status: status,
				},
			],
        },
        attributes: [
            "email", "roles", "first_name", "last_name"
        ],
        limit: parseInt(pageSize),
        offset: parseInt(start),
    })
}
"use strict";
let bcrypt = require("bcrypt");

module.exports = (sequelize, DataTypes) => {
	const customer = sequelize.define(
		"customer",
		{
			customer_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			// customer_name: DataTypes.STRING(50),
			storage_customer_id: DataTypes.STRING(150),
			first_name: DataTypes.STRING(50),
			last_name: DataTypes.STRING(50),
			company_id: DataTypes.INTEGER,
			photo: DataTypes.TEXT,
			address1: DataTypes.STRING,
			address2: DataTypes.STRING,
			city: DataTypes.STRING,
			state: DataTypes.STRING,
			zipCode: DataTypes.STRING,
			country: DataTypes.STRING,
			phone: DataTypes.STRING(15),
			phone2: DataTypes.STRING(15),
			phone3: DataTypes.STRING(15),
			country_code: DataTypes.STRING(10),
			email: DataTypes.STRING,
			email2: DataTypes.STRING(25),
			password: DataTypes.STRING,
			total_shipment: DataTypes.INTEGER,
			push_notification: DataTypes.TINYINT,
			is_verified: DataTypes.TINYINT,
			is_invited: {
				type: DataTypes.ENUM("INVITED", "VIEWED", "REGISTERED"),
				allowNull: true,
			},
			account_id: DataTypes.STRING(150),
			account_name: DataTypes.STRING(150),
			sales_rep: DataTypes.STRING(120),
			notes: DataTypes.TEXT,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			is_deleted: { type: DataTypes.TINYINT, defaultValue: 0 },
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			passwordTokenExpire: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			indexes: [
				{
					unique: true,
					fields: ["email"],
				},
			],
			hooks: {
				beforeCreate: (customer) => {
					if (
						customer.password &&
						customer.password !== "" &&
						customer.password !== "undefined"
					) {
						const salt = bcrypt.genSaltSync(10);
						customer.password = bcrypt.hashSync(customer.password, salt);
					}
				},
			},
		}
	);
	customer.associate = function (models) {
		// associations can be defined here
		customer.hasMany(models.shipment_job, { foreignKey: "customer_id" });
		customer.belongsTo(models.company, {
			as: "customer_company",
			foreignKey: "company_id",
		});
		customer.hasMany(models.tag_customer, {
			as: "customer_tag",
			foreignKey: "customer_id",
		});
	};
	return customer;
};

"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_exception = sequelize.define(
		"shipment_inventory_exception",
		{
			shipment_inventory_exception_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_inventory_id: DataTypes.INTEGER,
			exception_note_id: DataTypes.INTEGER,
			shipment_exception_id: DataTypes.INTEGER,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_inventory_exception.associate = function (models) {
		// associations can be defined here
		shipment_inventory_exception.belongsTo(models.shipment_inventory, {
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory_exception.belongsTo(
			models.shipment_inventory_exception_note,
			{ foreignKey: "exception_note_id" }
		);

		shipment_inventory_exception.belongsTo(models.shipment_exception, {
			as: "exception_list",
			foreignKey: "shipment_exception_id",
		});
	};
	return shipment_inventory_exception;
};

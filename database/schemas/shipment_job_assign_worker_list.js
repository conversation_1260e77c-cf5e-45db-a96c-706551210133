"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_job_assign_worker_list = sequelize.define(
		"shipment_job_assign_worker_list",
		{
			assign_job_worker_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_job_id: DataTypes.INTEGER,
			local_shipment_stage_id: DataTypes.INTEGER,
			staff_id: DataTypes.INTEGER,
			role: {
				type: DataTypes.ENUM("worker", "supervisor"),
				defaultValue: "worker",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_job_assign_worker_list.associate = function (models) {
		// associations can be defined here

		shipment_job_assign_worker_list.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});

    shipment_job_assign_worker_list.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_shipment_stage_id",
		});

		shipment_job_assign_worker_list.belongsTo(models.staff, {
			as: "assign_worker_detail",
			foreignKey: "staff_id",
		});
	};
	return shipment_job_assign_worker_list;
};

const { room_iso_code } = require("../../database/schemas");
const { Op } = require("sequelize");

// Get all room ISO codes
exports.getAllRoomIsoCodes = async () => {
	return await room_iso_code.findAll({
		attributes: ['id', 'name', 'code', 'annex'],
		raw: true
	});
};

// Create single room ISO code
exports.createRoomIsoCode = async (roomIsoData) => {
	return await room_iso_code.create({
		name: roomIsoData.name,
		code: roomIsoData.code,
		annex: roomIsoData.annex,
		created_at: new Date(),
		updated_at: new Date()
	});
};

// Bulk create room ISO codes
exports.bulkCreateRoomIsoCodes = async (roomIsoDataArray) => {
	// Prepare data for bulk insert
	const bulkData = roomIsoDataArray.map(item => ({
		name: item.name,
		code: item.code,
		annex: item.annex,
		created_at: new Date(),
		updated_at: new Date()
	}));
	
	return await room_iso_code.bulkCreate(bulkData);
};

// Clear all room ISO codes (for fresh import)
exports.clearAllRoomIsoCodes = async () => {
	return await room_iso_code.destroy({
		where: {},
		truncate: true
	});
};

// Get room ISO code by ID
exports.getRoomIsoCodeById = async (id) => {
	return await room_iso_code.findByPk(id);
};

// Update room ISO code
exports.updateRoomIsoCode = async (id, updateData) => {
	return await room_iso_code.update(
		{
			name: updateData.name,
			code: updateData.code,
			annex: updateData.annex,
			updated_at: new Date()
		},
		{ where: { id: id } }
	);
};

// Delete room ISO code
exports.deleteRoomIsoCode = async (id) => {
	return await room_iso_code.destroy({
		where: { id: id }
	});
};

// Check if room ISO code exists by code
exports.checkRoomIsoCodeExists = async (code) => {
	const count = await room_iso_code.count({
		where: { code: code }
	});
	return count > 0;
};

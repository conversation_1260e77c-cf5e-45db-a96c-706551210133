const axios = require('axios');

async function testCheckItemIsoCodeNames() {
    try {
        console.log("🔍 Testing Check Item ISO Code Names in Item Suggestion Table API");
        console.log("================================================================");
        
        // API endpoint
        const apiUrl = 'http://localhost:8421/api/admin/item/check-iso-code-names-in-suggestion';
        
        console.log("📡 Making API call to:", apiUrl);
        console.log("📋 Expected behavior:");
        console.log("   1. Get all names from item_iso_code table");
        console.log("   2. Check if each name exists in item_suggestion table");
        console.log("   3. Return list of names that exist and don't exist");
        console.log("   4. Provide summary statistics");
        console.log("\n⏳ Processing...\n");
        
        // Make API call
        const response = await axios.post(apiUrl, {}, {
            timeout: 60000 // 60 seconds timeout
        });
        
        console.log("✅ API Response Status:", response.status);
        
        if (response.data.status === 1) {
            console.log("🎉 API call successful!");
            console.log("📊 CHECK RESULTS:");
            console.log("================================================================");
            
            const data = response.data.data;
            
            // Display summary
            console.log("📈 SUMMARY:");
            console.log("===========");
            console.log(`📋 Total ISO codes checked: ${data.summary.total_checked}`);
            console.log(`✅ Found in item_suggestion: ${data.summary.found_in_suggestion}`);
            console.log(`❌ Not found in item_suggestion: ${data.summary.not_found_in_suggestion}`);
            console.log(`📊 Match percentage: ${data.summary.percentage_found}`);
            
            // Display items that exist in item_suggestion
            if (data.exists_in_suggestion.length > 0) {
                console.log("\n✅ NAMES THAT EXIST IN ITEM_SUGGESTION:");
                console.log("========================================");
                data.exists_in_suggestion.forEach((item, index) => {
                    console.log(`\n   ${index + 1}. ISO Code ID: ${item.id}`);
                    console.log(`      Name: "${item.name}"`);
                    console.log(`      Code: "${item.code}"`);
                    
                    if (item.matching_suggestions && item.matching_suggestions.length > 0) {
                        console.log(`      Matching suggestions (${item.matching_suggestions.length}):`);
                        item.matching_suggestions.forEach((suggestion, suggIndex) => {
                            console.log(`        ${suggIndex + 1}. ID: ${suggestion.item_suggestion_id}, Status: ${suggestion.status}, Company: ${suggestion.company_id}`);
                        });
                    }
                });
            } else {
                console.log("\n✅ NAMES THAT EXIST IN ITEM_SUGGESTION: None");
            }
            
            // Display items that don't exist in item_suggestion
            if (data.not_exists_in_suggestion.length > 0) {
                console.log("\n❌ NAMES THAT DON'T EXIST IN ITEM_SUGGESTION:");
                console.log("==============================================");
                data.not_exists_in_suggestion.forEach((item, index) => {
                    console.log(`   ${index + 1}. ISO Code ID: ${item.id}, Name: "${item.name}", Code: "${item.code}"`);
                });
            } else {
                console.log("\n❌ NAMES THAT DON'T EXIST IN ITEM_SUGGESTION: None");
            }
            
            // Show first few examples for quick reference
            console.log("\n🔍 QUICK REFERENCE:");
            console.log("===================");
            
            if (data.exists_in_suggestion.length > 0) {
                console.log("✅ First 3 names that EXIST:");
                data.exists_in_suggestion.slice(0, 3).forEach((item, index) => {
                    console.log(`   ${index + 1}. "${item.name}" (Code: ${item.code})`);
                });
            }
            
            if (data.not_exists_in_suggestion.length > 0) {
                console.log("❌ First 3 names that DON'T EXIST:");
                data.not_exists_in_suggestion.slice(0, 3).forEach((item, index) => {
                    console.log(`   ${index + 1}. "${item.name}" (Code: ${item.code})`);
                });
            }
            
            console.log("\n✅ SUCCESS: Item ISO code names check completed!");
            
        } else {
            console.log("⚠️ API returned error:", response.data.message);
            console.log("📄 Full response:", JSON.stringify(response.data, null, 2));
        }
        
    } catch (error) {
        console.log("\n❌ ERROR OCCURRED:");
        console.log("===============================================");
        
        if (error.response) {
            console.log("📡 API Error Response:");
            console.log("Status:", error.response.status);
            console.log("Message:", error.response.data?.message || 'No message');
            console.log("Full Response:", JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log("🌐 Network Error - No response received");
            console.log("Make sure the server is running on port 8421");
            console.log("Command to start server: npm start or node server.js");
        } else {
            console.log("⚠️ Error:", error.message);
        }
    }
}

// Instructions for testing
console.log("📋 TESTING INSTRUCTIONS:");
console.log("========================");
console.log("1. Make sure your server is running: npm start");
console.log("2. Ensure both item_iso_code and item_suggestion tables have data");
console.log("3. The API will compare names from both tables (case-insensitive)");
console.log("4. Authentication is disabled for testing");
console.log("5. Check server console for detailed processing logs");
console.log("6. This API helps identify which ISO code names need to be added to item_suggestion table\n");

// Run the test
console.log("🚀 Running check...");
testCheckItemIsoCodeNames();

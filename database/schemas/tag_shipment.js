"use strict";
module.exports = (sequelize, DataTypes) => {
  const tag_shipment = sequelize.define(
    "tag_shipment",
    {
      tag_shipment_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tag_id: DataTypes.INTEGER,
      shipment_id: DataTypes.INTEGER
    },
    { createdAt: false, updatedAt: false, freezeTableName: true }
  );
  tag_shipment.associate = function (models) {
    tag_shipment.belongsTo(models.tag,{foreignKey: 'tag_id', as: "m2m_tag"});
    tag_shipment.belongsTo(models.shipment_job,{foreignKey: 'shipment_job_id', as: "m2m_shipment"});
  };
  return tag_shipment;
};

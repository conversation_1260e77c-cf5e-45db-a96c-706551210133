"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_forced = sequelize.define(
		"shipment_inventory_forced",
		{
			forced_status_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			reason: DataTypes.STRING,
			shipment_inventory_id: DataTypes.INTEGER,
			current_stage_id: { type: DataTypes.INTEGER },
			local_current_stage_id: { type: DataTypes.INTEGER },

			override_by_staff: { type: DataTypes.INTEGER, allowNull: false },
			shipment_job_id: { type: DataTypes.INTEGER, allowNull: false },
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now(),
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now(),
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			freezeTableName: true,
		}
	);

	shipment_inventory_forced.associate = function (models) {
		// associations can be defined here
		shipment_inventory_forced.belongsTo(models.shipment_type_stage, {
			foreignKey: "current_stage_id",
		});
		shipment_inventory_forced.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_current_stage_id",
		});
		shipment_inventory_forced.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});
		shipment_inventory_forced.belongsTo(models.shipment_inventory, {
			as: "inventory_id",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory_forced.belongsTo(models.staff, {
			// as: "by_staff_id",
			foreignKey: "override_by_staff",
		});
	};
	return shipment_inventory_forced;
};

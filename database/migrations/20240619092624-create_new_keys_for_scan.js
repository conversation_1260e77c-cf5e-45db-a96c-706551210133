'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'total_remove_items_to_inventory_scan',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'total_add_items_to_inventory_scan',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'total_remove_items_to_inventory_scan');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'total_add_items_to_inventory_scan');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

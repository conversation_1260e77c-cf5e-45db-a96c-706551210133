"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'assign_to_storage_stage_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "remove_from_storage_stage_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "remove_from_inventory_stage_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "assign_to_storage_stage_id");
      await queryInterface.removeColumn("shipment_inventories", "remove_from_storage_stage_id");
      await queryInterface.removeColumn("shipment_inventories", "remove_from_inventory_stage_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_rooms",
        "createdByUserRole",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_rooms", "createdByUserRole");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

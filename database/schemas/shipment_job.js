"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_job = sequelize.define(
		"shipment_job",
		{
			shipment_job_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			storage_shipment_job_id: DataTypes.STRING(150),
			warehouseId: DataTypes.STRING(150),
			shipment_name: DataTypes.STRING(200),
			job_number: DataTypes.STRING,
			shipment_type_id: DataTypes.INTEGER,
			company_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			email: DataTypes.STRING,
			local_shipment_type_id: DataTypes.INTEGER,
			customer_id: DataTypes.INTEGER,
			pickup_address: DataTypes.TEXT,
			pickup_address2: DataTypes.TEXT,
			pickup_city: DataTypes.STRING,
			pickup_state: DataTypes.STRING,
			pickup_zipcode: DataTypes.STRING,
			pickup_country: DataTypes.STRING,
			delivery_address: DataTypes.TEXT,
			delivery_address2: DataTypes.TEXT,
			delivery_city: DataTypes.STRING,
			delivery_state: DataTypes.STRING,
			delivery_zipcode: DataTypes.STRING,
			delivery_country: DataTypes.STRING,
			external_reference: DataTypes.STRING,
			external_reference_2: DataTypes.STRING,
			external_reference_3: DataTypes.STRING,
			pickup_date: DataTypes.DATE,
			delivery_date: DataTypes.DATE,
			estimated_weight: DataTypes.DECIMAL(11, 2),
			estimated_volume: DataTypes.DECIMAL(11, 2),
			notes: DataTypes.TEXT,
			shipment_summary: DataTypes.TEXT,
			warehouse: DataTypes.STRING(150),
			job_status: DataTypes.INTEGER, // foreign key with dynamic stages from shipment type stages.
			local_job_status: DataTypes.INTEGER, // foreign key with dynamic stages from shipment type stages of shipment.

			total_items: DataTypes.INTEGER,
			total_cartons: DataTypes.INTEGER,
			total_cartons_cp: DataTypes.INTEGER,
			total_cartons_pbo: DataTypes.INTEGER,
			firearms_total_quantity: DataTypes.INTEGER,
			total_volume: DataTypes.DECIMAL(11, 2),
			total_weight: DataTypes.DECIMAL(11, 2),
			total_high_value: DataTypes.DECIMAL(11, 2),
			total_pads_used: DataTypes.INTEGER,
			total_pro_gear_items: DataTypes.INTEGER,
			total_pro_gear_weight: DataTypes.DECIMAL(11, 2),
			damaged_items: DataTypes.INTEGER,
			contact_reference: DataTypes.STRING,
			account_reference: DataTypes.STRING,
			opportunity_reference: DataTypes.STRING,
			move_coordinator: DataTypes.STRING,
			source: DataTypes.STRING,
			wo_reference: DataTypes.STRING,
			is_job_complete_flag: DataTypes.TINYINT,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			created_by_type: {
				type: DataTypes.ENUM("company", "staff"),
				defaultValue: "company",
			},
			created_by_id: DataTypes.INTEGER,
			created_at: {
				type: "TIMESTAMP",
			},
			updated_at: {
				type: "TIMESTAMP",
			},
		},
		{
			timestamps: true,
			createdAt: false,
			updatedAt: false,
			paranoid: true,
			indexes: [
				{
					unique: true,
					fields: ["phone"],
				},
				{
					unique: true,
					fields: ["phone2"],
				},
				{
					unique: true,
					fields: ["phone3"],
				},
				{
					unique: true,
					fields: ["email"],
				},
				{
					unique: true,
					fields: ["email2"],
				},
				{
					unique: true,
					fields: ["email"],
				},
			],
		}
	);
	shipment_job.associate = function (models) {
		// associations can be defined here

		shipment_job.belongsTo(models.shipment_type, {
			foreignKey: "shipment_type_id",
		});

		shipment_job.belongsTo(models.shipment_type_for_shipment, {
			foreignKey: "local_shipment_type_id",

		});


		shipment_job.belongsTo(models.customer, {
			as: "customer_job",
			foreignKey: "customer_id",
		});

		shipment_job.belongsTo(models.company, {
			as: "job_company",
			foreignKey: "company_id",
		});

		shipment_job.belongsTo(models.shipment_type_stage, {
			as: "shipment_job_status",
			foreignKey: "job_status",
		});

		shipment_job.belongsTo(models.shipment_type_stage_for_shipment, {

			as: "local_shipment_job_status",

			foreignKey: "local_job_status",

		});


		shipment_job.hasMany(models.shipment_job_assign_worker, {
			as: "job_worker",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.shipment_job_assign_worker_list, {
			as: "assign_worker",
			foreignKey: "shipment_job_id",
		});


		shipment_job.hasMany(models.shipment_inventory, {
			as: "job_items",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.shipment_inventory_unit_history, {
			as: "job_item_history",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.shipment_job_forced, {
			as: "forced_stages",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.unit_list, {
			as: "job_units",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.unit_list, {
			as: "job_warehouses",
			sourceKey: "warehouseId",
			foreignKey: "warehouseId",
		});

		shipment_job.hasMany(models.qr_code, {
			as: "job_qr",
			foreignKey: "job_id",
		});

		shipment_job.hasMany(models.shipment_job_signature, {
			as: "job_signature",
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.tag_shipment, {
			as: "shipment_tag",
			foreignKey: "shipment_id",
		});

		shipment_job.hasMany(models.shipment_type_stage_for_shipment, {
			foreignKey: "shipment_job_id",
		});

		shipment_job.hasMany(models.shipment_job_document, {
			as: "job_documents",
			foreignKey: "shipment_job_id",
		});

	};
	return shipment_job;
};

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "tag",
        "createdByUserRole",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("tag", "createdByUserRole");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

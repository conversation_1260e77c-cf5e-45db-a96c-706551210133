// Simple script to examine the Excel file structure
const ExcelJS = require("exceljs");
const fs = require("fs");

async function examineExcel() {
    try {
        const filePath = "./docs/shipment_rooms.xlsx";
        
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            console.log("❌ File not found:", filePath);
            return;
        }
        
        console.log("📁 Reading Excel file:", filePath);
        console.log("📊 File size:", fs.statSync(filePath).size, "bytes");
        
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        console.log("📋 Number of worksheets:", workbook.worksheets.length);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            console.log("❌ No worksheet found");
            return;
        }
        
        console.log("📄 Worksheet name:", worksheet.name || "Sheet1");
        console.log("📏 Dimensions:", `${worksheet.rowCount} rows x ${worksheet.columnCount} columns`);
        
        console.log("\n📋 First 15 rows:");
        console.log("Row | Column A (Room Name)     | Column B (ISO Code)");
        console.log("-".repeat(60));
        
        let validDataCount = 0;
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber <= 15) {
                const cellA = row.getCell(1).value;
                const cellB = row.getCell(2).value;
                
                const roomName = cellA ? String(cellA).substring(0, 20) : '';
                const isoCode = cellB ? String(cellB) : '';
                
                console.log(`${rowNumber.toString().padStart(3)} | ${roomName.padEnd(20)} | ${isoCode}`);
                
                // Count valid data (skip header row)
                if (rowNumber > 1 && cellA && cellB) {
                    validDataCount++;
                }
            }
        });
        
        console.log("\n📊 Summary:");
        console.log(`   - Total rows: ${worksheet.rowCount}`);
        console.log(`   - Valid data rows (excluding header): ${validDataCount}`);
        console.log(`   - Expected format: Column A = Room Name, Column B = ISO Code`);
        
        // Show some sample data for verification
        console.log("\n🔍 Sample valid entries:");
        let sampleCount = 0;
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1 && sampleCount < 5) {
                const roomName = row.getCell(1).value;
                const isoCode = row.getCell(2).value;
                
                if (roomName && isoCode) {
                    console.log(`   ${sampleCount + 1}. "${String(roomName).trim()}" -> "${String(isoCode).trim()}"`);
                    sampleCount++;
                }
            }
        });
        
        console.log("\n✅ Excel file examination complete!");
        
    } catch (error) {
        console.error("❌ Error examining Excel file:", error.message);
    }
}

examineExcel();

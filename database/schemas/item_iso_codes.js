"use strict";
module.exports = (sequelize, DataTypes) => {
	const item_iso_codes = sequelize.define(
		"item_iso_codes",
		{
			id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			code: DataTypes.STRING,
			created_at: {
				type: DataTypes.DATE,
			},
			updated_at: {
				type: DataTypes.DATE,
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	item_iso_codes.associate = function (models) {
	};
	return item_iso_codes;
};

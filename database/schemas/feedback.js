"use strict";
module.exports = (sequelize, DataTypes) => {
	const feedback = sequelize.define(
		"feedback",
		{
			feedback_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			experience: DataTypes.TEXT,
			feedback: DataTypes.TEXT,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	feedback.associate = function (models) {
		// associations can be defined here
	};
	return feedback;
};

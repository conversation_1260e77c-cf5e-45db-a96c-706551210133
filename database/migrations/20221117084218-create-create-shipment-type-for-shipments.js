"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_type_for_shipments", {
      local_shipment_type_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      ref_shipment_type_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      number_of_stages: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      signature_require: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "active",
      },
      createdByUserRole: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      is_deleted: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      admin_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      staff_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_type_for_shipments");
  },
};

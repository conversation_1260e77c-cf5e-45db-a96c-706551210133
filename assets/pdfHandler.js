const axios = require("axios");
const fs = require("fs");
const PDFDocument = require("pdfkit");
const moment = require("moment");
const dateFormat = "MM/DD/YYYY";

class PDFGeneration {
	state;
	FB = "Helvetica-Bold";
	FN = "Helvetica";
	pdfPath = "/tmp/joke.pdf";
	doc = new PDFDocument({ margin: 50, size: "A4", bufferPages: true }).font(
		this.FN
	);
	lastHr = 400;
	constructor(dumpingData, pdfPath) {
		this.axios = axios;
		this.state = dumpingData;
		this.shipment = dumpingData.shipment_type.shipment_stage;
		this.pdfPath = pdfPath;
	}

	createPDF = async () => {
		await this.generateFirstPage();
		this.doc.addPage();
		this.generateCenterTable();
		this.generatePageNumber();
		this.doc.addPage();
		await this.generateLastTable();
		this.generatePageNumber();
		this.doc.end();
		return this.doc.pipe(fs.createWriteStream(this.pdfPath));
	};

	generateLastTableRow = (y, c1, c2, c3, c4) => {
		const nameLength = c1.length <= 24;
		this.doc
			.fontSize(8)
			.font(this.FN)
			.text(c1, 56, y - 10, { width: 100, align: "center" })
			.image(c2, 185, y - 30, {
				width: 75,
				height: 45,
				// align: "center",
				// valign: "center",
				// width: 250,
				// align: "center",
			})
			.image(c3, 310, y - 30, {
				width: 75,
				height: 45,
				// width: 350,
				// align: "center",
			})
			.fontSize(8)
			.font(this.FN)
			.text(c4, 215, y - 10, { width: 500, align: "center" });

		this.lastHr = 13 + y + 8 - 48;
		this.generateHr(13 + y + 8);
		return nameLength;
	};
	generateLastTable = async () => {
		let tableTop = 0;
		let contentPerPage = 21;
		let rowPointer = false;
		let totalJobItems = this.shipment.length;

		for (let [count, item] of this.shipment.entries()) {
			let circularCount = (count + 1) % contentPerPage;
			if (count === 0 || circularCount === 0) {
				this.generateHeadline();

				if (circularCount === 0) {
					this.doc.addPage();
					if (totalJobItems - count > contentPerPage) {
						this.doc
							.strokeColor("#d4d0d0")
							.rect(50, 50, 500, contentPerPage * 24.9)
							.stroke();
					}
					tableTop = 0;
				}

				if (count === 0) {
					tableTop = 70;
					this.doc
						.fontSize(20)
						.font(this.FB)
						.fillColor("#292727")
						.text("Signature History", 55, tableTop)
						.fontSize(12)
						.font(this.FN)
						.moveDown(0.1)
						.strokeColor("#d4d0d0")
						.rect(50, 120, 500, contentPerPage * 23.9)
						.stroke();
				}
				tableTop += 60;
				this.generateSignatureHistory(tableTop);
				// tableTop += 18;
				// this.generateBottomFooter();
			}
			let img =
				"https://openxcell-development-public.s3.ap-south-1.amazonaws.com/mover-inventory/jobItem/159/original/2a50e3843d0a13a4f5b1eedbacf5463c.jpg";
			let imgURL = item.supervisor_signature ? item.supervisor_signature : img;
			let imgURL1 = item.customer_signature ? item.customer_signature : img;
			let image = await axios.default(imgURL, { responseType: "arraybuffer" });
			let image1 = await axios.default(imgURL1, {
				responseType: "arraybuffer",
			});

			tableTop += rowPointer && count !== 1 ? 50 : 18;
			let val = rowPointer ? 70 : 35;
			let position = tableTop + val + circularCount * 5;
			rowPointer = this.generateLastTableRow(
				position,
				item.name,
				image.data,
				image1.data,
				item.created_at ? moment(item.created_at).format(dateFormat) : " - "
			);
		}
	};
	countException = (data) => {
		let count = 0;
		data.forEach((data) => {
			if (data.shipment_inventory_exceptions.length > 0) {
				count = count + 1;
			}
		});
		return count;
	};
	countDisassembled = (data) => {
		let count = 0;
		data.forEach((data) => {
			if (data.disassembled_user_id || data.disassembled_by === "By Company" || data.disassembled_user_id !== "") {
				count = count + 1;
			}
		});
		return count;
	};
	countElectronics = (data) => {
		let count = 0;
		data.forEach((data) => {
			if (data.serial_number || data.serial_number !== "") {
				count = count + 1;
			}
		});
		return count;
	};
	formatPhoneNumber = (phoneNumberString) => {
		let cleaned = ('' + phoneNumberString).replace(/\D/g, '');
		let match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
		if (match) {
			return '(' + match[1] + ') ' + match[2] + '-' + match[3];
		}
		return null;
	};
	generateFirstPage = async () => {
		this.generateHeadline();
		const job_items = this.state.job_items;
		const company = this.state.job_company;
		const jobNumber = this.state.job_number;
		const contactNumber = this.state.customer_job.phone;
		const email = this.state.email;
		const getUrl = this.state.job_company.photo ? Const_AWS_BASE_Company_Profile + 'original/' + this.state.job_company.photo : "";
		const customerName =
			this.state.customer_job.first_name + this.state.customer_job.last_name;
		const logo = getUrl && getUrl !== "" ? await axios.default(getUrl, { responseType: "arraybuffer" }) : `${__dirname}/static/mi.png`;
		const job_worker = this.state.job_worker;
		let supervisor = job_worker.filter(worker => worker.role === "supervisor").map(worker => worker.first_name.trim() + " " + worker.last_name);
		let worker = job_worker.filter(worker => worker.role === "worker").map(worker => worker.first_name.trim() + " " + worker.last_name);
		let width;
		let yContent;
		let xContent = 65;
		const yBaseLine = 100;
		const nextRowPush = 100;
		const smallFont = 14;

		this.doc
			.image(logo && logo.data ? logo.data : logo, 65, 62, { width: 120, height: 100 });

		this.doc
			.font(this.FB)
			.fontSize(smallFont)
			.text(`${company.company_name}`, 200, 65, {
				align: "left",
			})
			.font(this.FN)
			.fontSize(smallFont - 4)
			.text(
				`${this.state.pickup_address + " " + this.state.pickup_address2}`,
				200, 90,
				{
					align: "left"
				}
			)
			.moveDown()
			.text(
				`${this.state.pickup_city} ${this.state.pickup_state} ${this.state.pickup_zipcode ? ", " + this.state.pickup_zipcode : ""
				}`, 200, 105, {
				align: "left",
			}
			)
			.moveDown()
			.text(`${this.state.pickup_country}`, 200, 120, {
				align: "left",
			})
			.moveDown()
			.text(`${company && company.phone ? this.formatPhoneNumber(company.phone) : ""}`, 200, 140, {
				align: "left",
			})
			.text(`${company.email}`, 200, 155, {
				align: "left",
			});
		yContent = yBaseLine + nextRowPush;
		this.doc
			.fontSize(23)
			.font(this.FB)
			.fillColor("#292727")
			.text(`${jobNumber} ${customerName}`, xContent, yBaseLine + 100, {
				align: "left",
			})
			.fontSize(smallFont - 2)
			.font(this.FN)
			.text(`${this.state.shipment_name}`, xContent, yContent + 27, {
				align: "left",
			})
			.fontSize(smallFont - 2)
			.font(this.FN)
			.text(`${contactNumber ? this.formatPhoneNumber(contactNumber) : ""}  ${email}`, xContent, yContent + 45, {
				align: "left",
			});

		xContent += 1;
		yContent = yBaseLine + nextRowPush + 100;
		width = 50;
		this.doc
			.font(this.FB)
			.fontSize(smallFont)
			.text(`Pickup`, xContent, yContent, { width: width, align: "left" });
		this.generateHr(yContent + smallFont + 4, xContent, width);

		this.doc
			.font(this.FN)
			.fontSize(smallFont - 4)
			.text(
				this.state.pickup_date
					? moment(this.state.pickup_date).format(dateFormat)
					: "",
				(xContent),
				(yContent += 35)
			)
			.text(
				`${this.state.pickup_address + " " + this.state.pickup_address2}`,
				xContent,
				(yContent += 25),
				{
					width: 120,
				}
			)
			.moveDown()
			.text(
				`${this.state.pickup_city} ${this.state.pickup_state} ${this.state.pickup_zipcode ? ", " + this.state.pickup_zipcode : ""
				}`
			)
			.moveDown()
			.text(`${this.state.pickup_country}`)
			.moveDown();
		xContent += 200;
		yContent = yBaseLine + nextRowPush + 100;
		width = 80;
		this.doc
			.font(this.FB)
			.fontSize(smallFont)
			.text(`Delivery`, (xContent -= 5), yContent, {
				width: width,
				align: "left",
			});
		this.generateHr(yContent + smallFont + 4, xContent, width);

		const deliveryText =
			this.state.delivery_date
				? moment(this.state.delivery_date).format(dateFormat)
				: "";
		this.doc
			.font(this.FN)
			.fontSize(smallFont - 4)
			.text(deliveryText, (xContent), (yContent += 35))
			.text(
				`${this.state.delivery_address + " " + this.state.delivery_address2}`,
				xContent,
				yContent + 25
			)
			.moveDown()
			.text(
				`${this.state.delivery_state} ${this.state.delivery_city} ${this.state.delivery_zipcode ? ", " + this.state.delivery_zipcode : ""
				}`
			)
			.moveDown()
			.text(`${this.state.delivery_country}`)
			.moveDown();
		xContent = 60;
		yContent += nextRowPush + 35;
		width = 100;
		this.doc
			.font(this.FB)
			.fontSize(smallFont)
			.text(`Job Summary`, xContent, yContent, { width: width, align: "left" });
		this.generateHr(yContent + smallFont + 4, xContent, width);

		this.doc
			.font(this.FB)
			.fontSize(smallFont - 4)
			.text(
				`Total Items: ${this.state.total_items}`,
				(xContent),
				(yContent += 35)
			)
			.text(
				`Total Volume: ${this.state.total_volume} Cu.Ft.`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Disassembled Items: ${this.countDisassembled(job_items)}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`High Value Total: ${"$" + this.state.total_high_value}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Pro Gear Items: ${this.state.total_pro_gear_items}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Items With Exceptions: ${this.countException(job_items)}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Supervisor: ${supervisor[0] ? supervisor[0] : ""}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Workers: ${worker[0] + ", "}`,
				xContent,
				(yContent += 25)
			);
		for (let i = 1; i <= worker.length - 1; i++) {
			this.doc
				.font(this.FB)
				.fontSize(smallFont - 4)
				.text(
					`${worker[i] + ", "}`,
					xContent + 45,
					(yContent += 15)
				);
		}
		xContent += 200;
		yContent = yBaseLine + nextRowPush + 278;
		width = 100;
		this.doc
			.font(this.FB)
			.fontSize(smallFont - 4)
			.text(
				`Total Cartons: ${this.state.total_cartons + ' (CP ' + this.state.total_cartons_cp + ", PBO " + this.state.total_cartons_pbo + ')'}`,
				(xContent),
				(yContent += 25)
			)
			.text(
				`Total Weight: ${this.state.total_weight} lbs`,
				(xContent),
				(yContent += 25)
			)
			.text(
				`Electronics: ${this.countElectronics(job_items)}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Pads Used: ${this.state.total_pads_used}`,
				xContent,
				(yContent += 25)
			)
			.text(
				`Pro Gear Weight: ${this.state.total_pro_gear_weight} lbs`,
				xContent,
				(yContent += 25)
			);
	};

	generateHeadline = () => {
		const jobNumber = this.state.job_number;

		this.doc
			.fontSize(7.5)
			.font(this.FN)
			.fillColor("#292727")
			.text(moment().format(dateFormat), 40, 20, { align: "left" })
			.text(`Job No. #${jobNumber}`, 40, 20, { align: "right" });
	};

	generatePageNumber = () => {
		let pages = this.doc.bufferedPageRange();
		for (let i = 0; i < pages.count; i++) {
			this.doc.switchToPage(i);
			this.doc
				.fontSize(8)
				.text(
					`${i + 1}/${pages.count}`,
					this.doc.page.width - 65,
					this.doc.page.height - 30,
					{ align: "right", height: 10, width: 40 }
				);
		}
	};

	generateBottomFooter = () => {
		let yFixture = 680;
		let xFixture = 55;
		let tableWidth = 60;
		let tableHeight = 32;

		this.doc
			.fontSize(10)
			.font(this.FB)
			.text(
				"Warning: Before signing - Check you shipment and verify all items. Describe any loss or damage on this page and initial your notes, or complete DD Form 1850 for DOD Shipments.",
				xFixture,
				yFixture,
				{
					lineGap: 7,
				}
			);

		this.doc
			.strokeColor("#d4d0d0")
			.rect(
				xFixture - 10,
				yFixture + tableHeight * 1.35,
				500,
				tableHeight * 2.12
			)
			.stroke();
		for (let i = 0; i <= 1; i++) {
			if (i === 1) {
				xFixture += tableWidth;
				yFixture = 680;
			}

			yFixture += 15;
			this.doc
				.fontSize(10)
				.font(this.FB)
				.strokeColor("#d4d0d0", 10)
				.rect(
					i === 0 ? xFixture - 10 : xFixture,
					yFixture + tableHeight - 4,
					i === 0 ? xFixture - 5 : 60,
					tableHeight * 2.12
				)
				.stroke()
				.text(
					i === 0 ? "At Origin" : "At Destination",
					i === 0 ? xFixture - 5 : xFixture,
					(yFixture += tableHeight) + 21,
					{
						width: i === 0 ? 40 : 60,
						align: "center",
						baseline: "middle",
					}
				);

			this.doc
				.font(this.FN)
				.fontSize(9)
				.text(
					"Contractor. Carrier or Rep. (Driver)",
					(xFixture += tableWidth - (i === 0 ? 18 : -10)),
					yFixture
				)
				.rect(
					i === 0 ? xFixture - 2 : xFixture - 10,
					yFixture + tableHeight - 5,
					i === 0 ? tableWidth * 3.14 : tableWidth * 3.37,
					0
				)
				.stroke()
				.text("Customer or Authorized Agent", xFixture, yFixture + tableHeight);

			this.doc
				.font(this.FN)
				.fontSize(9)
				.rect(xFixture + tableWidth * 2.4, yFixture - 4, 0, tableHeight * 2.12)
				.stroke()
				.text("Date", (xFixture += tableWidth * 2.1), yFixture, {
					width: 60,
					align: "center",
				})
				.text("Date", xFixture, yFixture + tableHeight, {
					width: 60,
					align: "center",
				})
				.moveDown();
		}
	};

	generateHr = (y, xFrom = 50, wide = 500, width = 0.25) => {
		this.doc
			.strokeColor("#d4d0d0", 10)
			.lineWidth(width)
			.moveTo(xFrom, y)
			.lineTo(xFrom + wide, y)
			.stroke();
	};

	generateVr = (yFrom, xFrom = 50, width = 0.1) => {
		this.doc
			.strokeColor("#fa7700", 10)
			.lineWidth(width)
			.moveTo(xFrom, yFrom)
			.lineTo(xFrom, this.lastHr)
			.stroke();
	};

	generateVertical = (rowStart) => {
		this.generateVr(rowStart, 105);
		this.generateVr(rowStart, 165);
		this.generateVr(rowStart, 225);
		this.generateVr(rowStart, 270);
		this.generateVr(rowStart, 295);
		this.generateVr(rowStart, 327);
		this.generateVr(rowStart, 358);
		this.generateVr(rowStart, 405);
		this.generateVr(rowStart, 490);
	};

	generateTableLabels = (tableTop) => {
		this.doc
			.fontSize(9)
			.font(this.FN)
			.text("Label", 53, tableTop, { width: 25, align: "left" })
			.text("Item", 80, tableTop, { width: 50, align: "left" })
			.text("Room", 130, tableTop, { width: 50, align: "left" })
			.text("Carton", 180, tableTop, { width: 30, align: "left" })
			.text("Electronics", 215, tableTop, { width: 50, align: "left" })
			.text("High Value", 265, tableTop, { width: 30, align: "left" })
			.text("Pro Gear", 300, tableTop, { width: 30, align: "left" })
			.text("Disassembled by", 335, tableTop, {
				width: 60,
				align: "left",
			})
			.text("Exceptions", 400, tableTop, { width: 50, align: "left" })
			.text("Notes", 455, tableTop, { align: "left" });

		this.generateHr(tableTop + 20);
	};
	generateCenterTable = () => {
		let tableTop = 0;
		let contentPerPage = 21;
		let rowPointer = false;
		let totalJobItems = this.state.job_items.length;

		for (let [count, item] of this.state.job_items.entries()) {
			let circularCount = (count + 1) % contentPerPage;
			if (count === 0 || circularCount === 0) {
				this.generateHeadline();

				if (circularCount === 0) {
					this.doc.addPage();
					if (totalJobItems - count > contentPerPage) {
						this.doc
							.strokeColor("#d4d0d0")
							.rect(50, 50, 500, contentPerPage * 24.9)
							.stroke();
					}
					tableTop = 0;
				}

				if (count === 0) {
					// tableTop = 70;
					this.doc
						// .fontSize(20)
						// .font(this.FB)
						// .fillColor("#292727")
						// .text("Moving Items", 55, tableTop)
						// .fontSize(12)
						// .font(this.FN)
						// .moveDown(0.1)
						// .text(`(${this.state.job_items.length} Item Total)`)
						.strokeColor("#d4d0d0")
						.rect(50, 50, 500, contentPerPage * 23.9)
						.stroke();
				}
				tableTop += 60;
				this.generateTableLabels(tableTop);
				tableTop += 18;
				this.generateBottomFooter();
				// this.generateSignatureHistory();
			}
			// tableTop += 18;
			tableTop += rowPointer && count !== 1 ? 25 : 10;
			let val = rowPointer ? 25 : 10;
			let position = tableTop + val + circularCount * 5;
			rowPointer = this.generateTableRow(
				position,
				item.item_qr.label_number,
				item.item_name,
				item.room ? item.room.name : "-",
				item.is_carton,
				item.serial_number || item.serial_number !== "" ? item.serial_number : "No",
				item.is_high_value === "true" ? "$" + item.declared_value : "No",
				item.is_pro_gear === "true" ? item.pro_gear_weight + " Lbs" : "No",
				item.disassembled_by === 'By Customer' ? 'By Customer' : item.disassembled_by === "By Company" && item.disassembled_user.first_name ? item.disassembled_user.first_name + " " + item.disassembled_user.last_name : " - ",
				item.shipment_inventory_exceptions[0]
					? item.shipment_inventory_exceptions[0].exception_list.name
					: "-",
				item.notes ? item.notes : "-",
			);
		}
	};

	generateTableRow = (y, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10) => {
		const nameLength = c10 ? c10.length <= 24 : 20;
		this.doc
			.fontSize(8)
			.font(this.FN)
			.text(c1, 53, y - 10, { width: 25, align: "left" })
			.text(c2, 80, y - (c2.length <= 11 ? 10 : 5), {
				width: 50,
				align: "left",
			})
			.text(c3, 130, y - 15, { width: 50, align: "left" })
			.text(c4, 180, y - 10, { width: 30, align: "left" })
			.text(c5, 215, y - 10, { width: 50, align: "left" })
			.text(c6, 265, y - 10, { width: 30, align: "left" })
			.text(c7, 300, y - 10, { width: 30, align: "left" })
			// .fontSize(7)
			.text(c8, 335, y - 10, { width: 60, align: "left" })
			.text(c9, 400, y - (c9.length <= 24 ? 10 : 5), {
				// width: 50,
				width: 50,
				align: "left",
			})
			.text(c10, 455, y - (c10.length <= 10 ? 10 : 15), {
				align: "left",
			});
		// .fontSize(8)
		this.lastHr = 13 + y - 5 + (nameLength ? 0 : 8) - 48;
		this.generateHr(13 + y - 5 + (nameLength ? 0 : 8));
		return nameLength;
	};
	generateSignatureHistory = (tableTop) => {
		this.doc
			.fontSize(15)
			.font("Times-Bold")
			.text("Stage", 55, tableTop - 3, {
				width: 100,
				align: "center",
			})
			.text("User", 110, tableTop - 3, {
				width: 225,
				align: "center",
			})
			.text("Customer", 170, tableTop - 3, {
				width: 350,
				align: "center",
			})
			.text("Date/Time", 225, tableTop - 3, {
				width: 500,
				align: "center",
			});

		this.generateHr(tableTop + 20);
	};
}

module.exports = PDFGeneration;

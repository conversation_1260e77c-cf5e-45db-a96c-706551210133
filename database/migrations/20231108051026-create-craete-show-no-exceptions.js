'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stages',
        'show_no_exceptions',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 1,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'show_no_exceptions',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 1,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_type_stages', 'show_no_exceptions');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'show_no_exceptions');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

image: node:14

stages:
  - build
  - deploy

variables:
  PROJECT: "mover-inventory-api"
  TECHNOLOGY: "nodejs"

build:
  stage: build
  environment:
    name: $CI_COMMIT_REF_SLUG
  variables:
    BUILD_ARGS: "--build-arg APP_NAME=${PROJECT} --build-arg NODE_ENV=${CI_COMMIT_REF_NAME}"
    DOCKERFILE_PATH: "Dockerfile" 
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
  image:
    name: public.ecr.aws/a9h1o0f5/kaniko-image
    entrypoint: [""]
  script:
    #- if [ -z "$TARGET_PATH" ];then cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env;else echo "read env from s3";fi
    - cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env || ls -la
    - rm -rf .env.*; rm -rf env
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor --cache=true --context ${CONTEXT:-$CI_PROJECT_DIR} --dockerfile $CI_PROJECT_DIR/$DOCKERFILE_PATH $BUILD_ARGS --registry-mirror mirror.gcr.io --registry-mirror index.docker.io --destination $CI_REGISTRY_IMAGE:$IMAGE_TAG
  only:
    - master

deploy_dev:
  stage: deploy
  environment:
    name: $CI_COMMIT_REF_NAME
    url: https://staging.moverinventory-api.movegistics.com
  variables:
    CONT_PORT: "8421"
    USER: "ubuntu"
    DEPLOY_PATH: $PROJECT
    IP_ADDRESS: "************"
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
    DOCKER_COMPOSE_TEMPLATE: "https://gitlab.com/NikunjM/gitlab-ci/-/raw/main/docker-compose.yaml"
  image: 
    name: gotechnies/alpine-ssh
  before_script:
    - apk update && apk add --no-cache openssh-client
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker system prune -f
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} mkdir -p ${DEPLOY_PATH}
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk $DOCKER_COMPOSE_TEMPLATE | sed 's%__PROJECT_NAME__%${PROJECT}%g;s%__PROJECT_SLUG__%${PROJECT_SLUG}%g;s%__PORT__%${CONT_PORT}%g;s%__IMAGE_URI__%${CI_REGISTRY_IMAGE}%g;s%__IMAGE_TAG__%${IMAGE_TAG}%g' > ${DEPLOY_PATH}/docker-compose.yml"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose pull"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose up -d --force-recreate"  
  only:
    -  development

deploy_master:
  stage: deploy
  environment:
    name: $CI_COMMIT_REF_NAME
    url: https://moverinventory-api.movegistics.com
  variables:
    CONT_PORT: "8421"
    USER: "ubuntu"
    DEPLOY_PATH: $PROJECT
    IP_ADDRESS: "***********"
    IMAGE_TAG: $CI_COMMIT_REF_SLUG
    DOCKER_COMPOSE_TEMPLATE: "https://gitlab.com/NikunjM/gitlab-ci/-/raw/main/docker-compose.yaml"
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker system prune -f
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} mkdir -p ${DEPLOY_PATH}
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk $DOCKER_COMPOSE_TEMPLATE | sed 's%__PROJECT_NAME__%${PROJECT}%g;s%__PROJECT_SLUG__%${PROJECT_SLUG}%g;s%__PORT__%${CONT_PORT}%g;s%__IMAGE_URI__%${CI_REGISTRY_IMAGE}%g;s%__IMAGE_TAG__%${IMAGE_TAG}%g' > ${DEPLOY_PATH}/docker-compose.yml"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose pull"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose up -d --force-recreate"
  only:
    - master

'use strict';
module.exports = (sequelize, DataTypes) => {
  const shipment_inventory_notes = sequelize.define(
    'shipment_inventory_notes',

    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      shipment_inventory_id: DataTypes.INTEGER,
      note: DataTypes.STRING,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },

    },
    { createdAt: false, updatedAt: false }
  );
  shipment_inventory_notes.associate = function (models) {

    shipment_inventory_notes.belongsTo(models.shipment_inventory, {
      as:"shipment_inventory",
			foreignKey: "shipment_inventory_id",
		});
  };

  return shipment_inventory_notes;
};
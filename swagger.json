{"swagger": "2.0", "info": {"version": "1.0.0", "title": "Mover-Inventory Open-api documention", "description": "Documentation automatically generated by the <b>swagger-autogen</b> module."}, "host": "localhost:8421", "basePath": "/", "produces": ["application/json"], "paths": {"/api/open-api/customer/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/shipment-type/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/shipment/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/staff/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/tag/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/tag/list/customer": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/tag/list/shipment": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/qr_code/{jobId}/list": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}, {"name": "jobId", "in": "path", "required": true, "type": "integer"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/customer/add": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Customer Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "first_name": "Baraskar", "email": "<EMAIL>", "last_name": "Baraskar", "address1": "<PERSON><PERSON>", "address2": "Navagam , Dindoli Road", "city": "Surat", "state": "Gujarat", "zipCode": "394210", "country": "India", "phone": "9898989898", "phone2": "8989898989", "phone3": "9999999999", "country_code": "91", "email2": "<EMAIL>", "notes": "First Customer Details"}}], "responses": {"201": {"description": "Customer created successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/shipment": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Shipment Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "shipment_type_id": "451", "shipment_name": "MyShipment", "email": "<EMAIL>", "customer_id": "217", "pickup_address": "<PERSON><PERSON>", "pickup_address2": "Na<PERSON>gam", "pickup_city": "Surat", "pickup_state": "Gujarat", "pickup_zipcode": "394210", "pickup_country": "India", "delivery_address": "<PERSON>", "delivery_address2": "Kotra", "delivery_city": "Bhopal", "delivery_state": "Madhyprades<PERSON>", "delivery_zipcode": "40000", "delivery_country": "India", "estimated_weight": "100", "estimated_volume": "100", "notes": "My Shipment notes"}}], "responses": {"201": {"description": "Job Shipment created successfully!"}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/tag": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Tag Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "name": "Tag Name", "tag_for": "SHIPMENT", "color": "#e91e63"}}], "responses": {"201": {"description": "Tag added successfully!."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/qr_code/generate": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Qr Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "jobId": "600", "quantity": "5", "type": "1"}}], "responses": {"201": {"description": "QR/Label created!."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/mail_customer": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Shipment Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "shipmentId": "600"}}], "responses": {"201": {"description": "Mail sent successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/shipment/{shipmentId}/link/": {"put": {"parameters": [{"name": "obj", "in": "body", "description": "Shipment Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "staff_id": "145", "role": "worker", "model_customer_id": "217", "model_customer_email": "<EMAIL>"}}, {"name": "shipmentId", "in": "path", "required": true, "type": "integer"}], "responses": {"201": {"description": "Job Shipment updated successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/shipment/{shipmentId}/unlink/": {"put": {"parameters": [{"name": "obj", "in": "body", "description": "Shipment Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "staff_worker_id": "145", "role": "worker", "model_customer_id": "217", "model_customer_email": "<EMAIL>"}}, {"name": "shipmentId", "in": "path", "required": true, "type": "integer"}], "responses": {"201": {"description": "Job Shipment updated successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/shipment-details/{shipmentId}": {"post": {"produces": ["application/json"], "parameters": [{"name": "company_key", "in": "body", "required": true, "type": "string", "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}, {"name": "shipmentId", "in": "path", "required": true, "type": "integer"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/customer/view-customer": {"post": {"produces": ["application/json"], "parameters": [{"name": "obj", "in": "body", "description": "Customer Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "customer_id": "217"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/room/": {"post": {"produces": ["application/json"], "parameters": [{"name": "obj", "in": "body", "description": "Room Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "room_name": "MyRoom"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/item_suggestion/": {"post": {"produces": ["application/json"], "parameters": [{"name": "obj", "in": "body", "description": "item Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "item_name": "MyItem", "item_volume": "10", "item_weight": "10"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/room/list/": {"post": {"produces": ["application/json"], "parameters": [{"name": "obj", "in": "body", "description": "get Room List", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/item_suggestion/list/": {"post": {"produces": ["application/json"], "parameters": [{"name": "obj", "in": "body", "description": "get Item List", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/api/open-api/customer/edit-customer": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Customer Information", "required": true, "example": {"customer_id": "217", "company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "first_name": "Baraskar", "email": "<EMAIL>", "last_name": "Baraskar", "address1": "<PERSON><PERSON>", "address2": "Navagam , Dindoli Road", "city": "Surat", "state": "Gujarat", "zipCode": "394210", "country": "India", "phone": "9898989898", "phone2": "8989898989", "phone3": "9999999999", "country_code": "91", "email2": "<EMAIL>", "notes": "First Customer Details"}}], "responses": {"201": {"description": "Customer update successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/shipment/{shipmentId}": {"put": {"parameters": [{"name": "obj", "in": "body", "description": "Customer Information", "required": true, "example": {"company_key": "0BB5B7532530F3532DFAC8F79B8E7120", "shipment_name": "MyShipment", "email": "<EMAIL>", "customer_id": "217", "pickup_address": "<PERSON><PERSON>", "pickup_address2": "Na<PERSON>gam", "pickup_city": "Surat", "pickup_state": "Gujarat", "pickup_zipcode": "394210", "pickup_country": "India", "delivery_address": "<PERSON>", "delivery_address2": "Kotra", "delivery_city": "Bhopal", "delivery_state": "Madhyprades<PERSON>", "delivery_zipcode": "40000", "delivery_country": "India", "estimated_weight": "100", "estimated_volume": "100", "notes": "My Shipment notes"}}, {"name": "shipmentId", "in": "path", "required": true, "type": "integer"}], "responses": {"201": {"description": "Shipment update successfully."}, "500": {"description": "Internal Server Error"}}}}, "/api/open-api/allItemsList/{shipmentId}": {"post": {"parameters": [{"name": "obj", "in": "body", "description": "Shipment Items Information", "required": true, "example": {"company_key": "69227A1430A9B6BBE3B25309DD7C7951"}}, {"name": "shipmentId", "in": "path", "required": true, "type": "integer"}], "responses": {"201": {"description": "Items retrieved successfully."}, "500": {"description": "Internal Server Error"}}}}}}
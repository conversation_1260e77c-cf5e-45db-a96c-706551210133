'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn("shipment_job_signatures", 'stage', {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
      references: {
        model: "shipment_type_stages",
        key: "shipment_stage_id",
      }
    })
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn("shipment_job_signatures", 'stage', {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
      references: {
        model: "shipment_type_stages",
        key: "shipment_type_id",
      }
    });

  }
};

const roomModel = require("../../models/Admin/roomModel");
const roomIsoCodeModel = require("../../models/Admin/roomIsoCodeModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const { request } = require("../../routes/homeRoutes");
const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

exports.checkValidRoomController = async (request, response, next) => {
	try {
		const roomListing = await roomModel.checkValidRoomModel(request.body);
		if (roomListing) {
			next();
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.checkValidRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}

}

exports.getRoomListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomListing = await roomModel.getRoomListingModel(request.query, getUserDetails);
		if (roomListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_RETRIEVED_SUCCESS,
				roomListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.getRoomListingController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomController = async (request, response) => {
	try {
		const { room_name, admin_id, company_id, staff_id } = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const roomDetails = await roomModel.createRoomModel(room_name, admin_id, company_id, staff_id, getUserDetails);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_ADDED_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createRoomOnTheFlyController = async (request, response, next) => {
	let getUserDetails = await commonModel.getUserDetails(request);
	const { room_id, room_name, add_permanently } = request.body;

	if (room_id === -1 || room_id === "-1") {
		if (room_name === undefined || room_name === null || room_name === "") {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Room name can not be empty!",
				{}
			);
		}

		let roomNameDetails = await roomModel.getRoomViaNameModel(room_name, getUserDetails);
		if (!roomNameDetails) {
			let roomDetails = await roomModel.createRoomModelForAddItem(room_name, getUserDetails, add_permanently);
			if (roomDetails) {
				request.body.room_id = roomDetails.shipment_room_id;
				next();
			}
			else {
				commonFunction.generateResponse(
					response,
					SUCCESS_CODE,
					0,
					ROOM_ADDED_FAILURE,
					{}
				);
			}
		}
		else {
			request.body.room_id = roomNameDetails.shipment_room_id;
			next();
		}
	}
	else {
		next();
	}
};

exports.editRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const { room_name } = request.body;
		const roomDetails = await roomModel.editRoomModel(roomId, room_name);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_EDIT_SUCCESS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.editRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.changeShipmentRoomStatus = async (request, response) => {
	try {
		const { shipment_room_id } = request.body;
		const findOldStatusRoom = await roomModel.findOldStatusRoom(shipment_room_id);
		const roomDetails = await roomModel.changeShipmentRoomStatus(shipment_room_id);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				findOldStatusRoom.status == "Active" ? "Room deactivated successfully" : "Room activated successfully",
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STATUS_CHANGE_FAIL,
				{}
			);
	}
	catch (reason) {
		console.log("exports.changeShipmentRoomStatus -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.removeRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const data = await roomModel.removeRoomModel(roomId);
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.removeRoomController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.isValidRoomController = (request, response, next) => {
	try {
		const room_id = request.params.roomId
			? request.params.roomId
			: request.body.room_id
				? request.body.room_id
				: request.body.shipment_room_id;
		const isValidRoom = roomModel.checkRoomExistenceModel(room_id);
		if (isValidRoom) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	} catch (error) {
		console.log("exports.isValidRoomController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.viewRoomController = async (request, response) => {
	try {
		const { roomId } = request.params;
		const roomDetails = await roomModel.getRoomModel(roomId);
		if (roomDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ROOM_DETAILS,
				roomDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ROOM_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.viewRoomController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.checkRoomAssignToJob = async (request, response, next) => {
	const room_id = request.params.roomId
		? request.params.roomId
		: request.body.room_id
			? request.body.room_id
			: request.body.shipment_room_id;
	const { count } = await roomModel.checkRoomAssignToJob(room_id);
	if (count && count > 0) {
		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			0,
			request.body && request.body.isInactive ? "Cannot Inactive this Room as this room is in use inside Shipments!" : "Cannot delete this Room as this room is in use inside Shipments!",
			{}
		);
	} else {
		next();
	}
};


exports.batchRoomListStatusChange = async (request, response) => {
	try {
		let { roomList, isActiveFlag } = request.body;
		if (!isActiveFlag) {
			let roomDetails = await roomModel.checkRoomAssignToItems(roomList);
			roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		}
		await roomModel.batchRoomListStatusChange(roomList, isActiveFlag);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			isActiveFlag ? "Room activated successfully" : "Room deactivated successfully",
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchRoomListStatusChange -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

exports.batchDeleteRoomList = async (request, response) => {
	try {
		let { roomList } = request.body;
		let roomDetails = await roomModel.checkRoomAssignToItems(roomList);

		roomList = roomList.filter(roomId => !roomDetails.includes(roomId));
		await roomModel.batchDeleteRoomListStatus(roomList);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ROOM_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchDeleteRoomList -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}

// Read Shipment Rooms Excel File Controller
exports.readShipmentRoomsExcelController = async (request, response) => {
	console.log("🚀 ~ request:", request)
	try {
		console.log("🚀 Starting Shipment Rooms Excel File Reading Process");

		// Define the path to the Excel file in docs folder
		const excelFilePath = path.join(__dirname, "../../docs", "shipment_rooms.xlsx");

		// Check if file exists
		if (!fs.existsSync(excelFilePath)) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Shipment rooms Excel file not found in docs folder",
				{}
			);
		}

		console.log("📁 Excel file found at:", excelFilePath);
		console.log("📊 File size:", fs.statSync(excelFilePath).size, "bytes");

		// Read Excel file
		const workbook = new ExcelJS.Workbook();
		await workbook.xlsx.readFile(excelFilePath);

		const worksheet = workbook.getWorksheet(1);
		if (!worksheet) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No worksheet found in Excel file",
				{}
			);
		}

		console.log(`📊 Excel worksheet found with ${worksheet.rowCount} rows and ${worksheet.columnCount} columns`);

		// Extract headers from first row
		const headers = [];
		const headerRow = worksheet.getRow(1);
		for (let i = 1; i <= worksheet.columnCount; i++) {
			const headerValue = headerRow.getCell(i).value;
			if (headerValue) {
				headers.push(String(headerValue).trim());
			} else {
				headers.push(`column_${String.fromCharCode(64 + i)}`); // Fallback to column_A, column_B, etc.
			}
		}

		console.log("📋 Headers found:", headers);

		// Extract all data from Excel using headers as keys
		const excelData = [];
		worksheet.eachRow((row, rowNumber) => {
			const rowData = {};

			// Extract data from each column using header names
			for (let i = 1; i <= worksheet.columnCount; i++) {
				const cellValue = row.getCell(i).value;
				const headerName = headers[i - 1]; // Headers array is 0-indexed

				if (cellValue !== null && cellValue !== undefined) {
					rowData[headerName] = String(cellValue).trim();
				} else {
					rowData[headerName] = null;
				}
			}

			// Only add rows that have at least one non-empty cell
			const hasData = Object.values(rowData).some(value =>
				value !== null && value !== undefined && value !== ""
			);

			if (hasData) {
				excelData.push(rowData);
			}
		});

		console.log("📊 EXCEL READING SUMMARY:");
		console.log(`   - Total rows in file: ${worksheet.rowCount}`);
		console.log(`   - Data rows extracted: ${excelData.length}`);
		console.log(`   - Total columns: ${worksheet.columnCount}`);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Shipment rooms Excel file read successfully",
			excelData
		);

	} catch (error) {
		console.error("❌ Error in readShipmentRoomsExcelController:", error);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Internal server error while reading Excel file",
			{}
		);
	}
};

// Bulk Create Room ISO Codes from Excel Controller
exports.bulkCreateRoomIsoCodesFromExcelController = async (request, response) => {
	try {
		console.log("🚀 Starting Bulk Create Room ISO Codes from Excel Process");
		console.log("🚀 ~ request:", request.body);

		// Define the path to the Excel file in docs folder
		const excelFilePath = path.join(__dirname, "../../docs", "shipment_rooms.xlsx");

		// Check if file exists
		if (!fs.existsSync(excelFilePath)) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"Shipment rooms Excel file not found in docs folder",
				{}
			);
		}

		console.log("📁 Excel file found at:", excelFilePath);

		// Read Excel file
		const workbook = new ExcelJS.Workbook();
		await workbook.xlsx.readFile(excelFilePath);

		const worksheet = workbook.getWorksheet(1);
		if (!worksheet) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No worksheet found in Excel file",
				{}
			);
		}

		console.log(`📊 Excel worksheet found with ${worksheet.rowCount} rows`);

		// Extract headers from first row
		const headers = [];
		const headerRow = worksheet.getRow(1);
		for (let i = 1; i <= worksheet.columnCount; i++) {
			const headerValue = headerRow.getCell(i).value;
			if (headerValue) {
				headers.push(String(headerValue).trim());
			} else {
				headers.push(`column_${String.fromCharCode(64 + i)}`);
			}
		}

		console.log("📋 Headers found:", headers);

		// Extract data for room ISO codes
		const roomIsoData = [];
		worksheet.eachRow((row, rowNumber) => {
			// Skip header row
			if (rowNumber > 1) {
				const rowData = {};

				// Extract data from each column using header names
				for (let i = 1; i <= worksheet.columnCount; i++) {
					const cellValue = row.getCell(i).value;
					const headerName = headers[i - 1];

					if (cellValue !== null && cellValue !== undefined) {
						rowData[headerName] = String(cellValue).trim();
					} else {
						rowData[headerName] = null;
					}
				}

				// Only add rows that have at least one non-empty cell
				const hasData = Object.values(rowData).some(value =>
					value !== null && value !== undefined && value !== ""
				);

				if (hasData) {
					// Map Excel data to room_iso_code table structure
					// Based on your requirements:
					// - code: iso_full_id from Excel
					// - name: name from Excel
					// - annex: annex from Excel
					const roomIsoRecord = {
						code: rowData.iso_full_id || rowData.ISO_FULL_ID || rowData['ISO_FULL_ID'] || null,
						name: rowData.name || rowData.Name || rowData['Name'] || null,
						annex: rowData.annex || rowData.Annex || rowData['Annex'] || null
					};

					// Only add if we have at least code or name
					if (roomIsoRecord.code || roomIsoRecord.name) {
						roomIsoData.push(roomIsoRecord);
					}
				}
			}
		});

		if (roomIsoData.length === 0) {
			return commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"No valid room ISO code data found in Excel file",
				{}
			);
		}

		console.log(`📊 Found ${roomIsoData.length} valid room ISO code records`);
		console.log("🔍 Sample records:", roomIsoData.slice(0, 3));

		// Optional: Clear existing data if requested
		if (request.body.clear_existing === true || request.body.clear_existing === "true") {
			console.log("🗑️ Clearing existing room ISO codes...");
			await roomIsoCodeModel.clearAllRoomIsoCodes();
			console.log("✅ Existing room ISO codes cleared");
		}

		// Bulk create room ISO codes
		console.log("💾 Creating room ISO codes in bulk...");
		const createdRecords = await roomIsoCodeModel.bulkCreateRoomIsoCodes(roomIsoData);

		console.log("📊 BULK CREATE SUMMARY:");
		console.log(`   - Records processed from Excel: ${roomIsoData.length}`);
		console.log(`   - Records created in database: ${createdRecords.length}`);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			"Room ISO codes created successfully from Excel data",
			{
				total_excel_records: roomIsoData.length,
				total_created_records: createdRecords.length,
				created_records: createdRecords,
				sample_data: roomIsoData.slice(0, 5)
			}
		);

	} catch (error) {
		console.error("❌ Error in bulkCreateRoomIsoCodesFromExcelController:", error);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			error.message || "Internal server error while creating room ISO codes",
			{}
		);
	}
};

"use strict";
let bcrypt = require("bcrypt");

module.exports = (sequelize, DataTypes) => {
	const admin = sequelize.define(
		"admin",
		{
			admin_id: {
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			first_name: {
				type: DataTypes.STRING,
			},
			last_name: {
				type: DataTypes.STRING,
			},
			email: {
				type: DataTypes.STRING,
				unique: true,
			},
			password: {
				type: DataTypes.STRING,
			},
			is_verified: DataTypes.TINYINT,
			verification_code: DataTypes.STRING,
			gender: {
				type: DataTypes.ENUM,
				values: ["Male", "Female", "Other"],
				defaultValue: "Male",
			},
			notificationStatus: DataTypes.TINYINT,
			photo: DataTypes.TEXT,
			roles: {
				type: DataTypes.STRING,
				defaultValue: "SUPERADMIN",
			},
			status: {
				type: DataTypes.ENUM,
				values: ["Active", "Inactive"],
				defaultValue: "Active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			indexes: [
				{
					fields: ["email"],
					unique: true,
				},
			],
			hooks: {
				beforeCreate: (user) => {
					const salt = bcrypt.genSaltSync(10);
					user.password = bcrypt.hashSync(user.password, salt);
				},
			},
		}
	);
	admin.associate = function (models) {
		// associations can be defined here
	};
	return admin;
};

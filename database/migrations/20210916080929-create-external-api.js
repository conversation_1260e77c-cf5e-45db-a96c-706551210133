"use strict";
module.exports = {
	up: (queryInterface, Sequelize) => {
		return queryInterface.createTable("external_apis", {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			email: {
				type: Sequelize.STRING,
				allowNull: false,
				unique: "unique_tag",
			},
			password: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			type: {
				type: Sequelize.ENUM("CMS", "API"),
				defaultValue: "API",
			},
			from: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			apiKey: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			status: {
				type: Sequelize.ENUM,
				allowNull: true,
				values: ["Active", "Inactive"],
				defaultValue: "Active",
			},
			expire: {
				type: Sequelize.DATE,
				defaultValue: null,
				allowNull: true,
			},
			created_at: {
				type: Sequelize.DATE,
				defaultValue: new Date(),
			},
			updated_at: {
				type: Sequelize.DATE,
				defaultValue: new Date(),
			},
		});
	},
	down: (queryInterface, Sequelize) => {
		return queryInterface.dropTable("external_apis");
	},
};

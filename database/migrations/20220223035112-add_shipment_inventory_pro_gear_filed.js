"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_inventories",
        "firmarm_serial_number",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "progear_name",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "firmarm_serial_number");
      await queryInterface.removeColumn("shipment_inventories", "progear_name");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

"use strict";

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn(
      'customers',
      'is_invited',
      {
        type: Sequelize.ENUM( 'INVITED', 'VIEWED', "REGISTERED"),
        allowNull:true
      }
    );
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn(
      'customers',
      'is_invited',
      {
        type: Sequelize.ENUM('PENDING', 'INVITED', 'VIEWED'),
        defaultValue: 'PENDING'
      });
  },
};



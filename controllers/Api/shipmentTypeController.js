
const shipmentTypeModelApi = require("../../models/Api/shipmentTypeModel");
const shipmentModelApi = require("../../models/Api/shipmentModel");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");


exports.viewShipmentStageForShipment = async (request, response) => {
    try {

        const getShipmentTypeStageList = await shipmentTypeModelApi.getShipmentTypeStageList(request.body);
        if (getShipmentTypeStageList)
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: "Fetch shipment stage list successfully!",
                data: getShipmentTypeStageList,
            });
        else
            response.status(SERVER_ERROR_CODE).json({
                status: 0,
                message: "Shipment stage fetch error",
                data: {},
            });
    }
    catch (error) {
        console.log("exports.viewShipmentStageForShipment -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}


exports.addShipmentTypeStage = async (request, response) => {
    try {

        const getShipmentTypeStageList = await shipmentTypeModelApi.getShipmentTypeStageList(request.body);
        const addShipmentTypeStage = await shipmentTypeModelApi.addShipmentTypeStage(
            request.body,
            getShipmentTypeStageList
        );

        const shipmentTypestagesupdate = await shipmentTypeModelApi.shipmentTypestagesupdate(getShipmentTypeStageList);
        const shipmentJobCompleteFlagChange = await shipmentTypeModel.shipmentJobCompleteFlagChange(addShipmentTypeStage);

        if (addShipmentTypeStage)
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: "Shipment type stage created successfully!",
                data: addShipmentTypeStage,
            });
        else
            response.status(SERVER_ERROR_CODE).json({
                status: 0,
                message: SHIPMENT_TYPE_ADD_FAIL,
                data: {},
            });
    }
    catch (error) {
        console.log("exports.addShipmentTypeStage -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

exports.editShipmentStage = async (request, response) => {
    try {
        const fetchShipmentData = await shipmentModelApi.fetchShipmentData(request.body)

        if (fetchShipmentData.local_job_status == request.body.local_shipment_stage_id) {
            response.status(SUCCESS_CODE).json({
                status: 0,
                message: "Shipment current stage can not be updated",
                data: {},
            });
        }
        else {
            const shipmentStageDetail = await shipmentTypeModelApi.editShipmentStage(
                request.body
            );
            if (shipmentStageDetail) {
                response.status(SUCCESS_CODE).json({
                    status: 1,
                    message: SHIPMENT_STAGE_UPDATE_SUCCESS,
                    data: {},
                });
            }
            else {
                response.status(SUCCESS_CODE).json({
                    status: 0,
                    message: "Shipment current stage can not be updated",
                    data: {},
                });
            }
        }

    } catch (error) {
        console.log("exports.editShipmentStage -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};


/**Post /shipment-type/list listShipmentType Controller */
exports.listShipmentType = async (request, response) => {
    try {
        //here we fetch shipmentType
        let shipmentTypeList = await shipmentTypeModelApi.listShipmentType(request.query, request.body);
        if (shipmentTypeList) {
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: SHIPMENT_TYPE_RETRIEVED_SUCCESS,
                data: shipmentTypeList
            });
        } else {
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: SHIPMENT_TYPE_NOT_FOUND,
                data: {},
            });
        }
    } catch (error) {
        console.log("exports.listShipmentType -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};

exports.addShipmentType = async (request, response) => {
    try {
        const addShipmentTypeData = await shipmentTypeModelApi.addShipmentType(
            request.body,
        );
        if (addShipmentTypeData)
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: SHIPMENT_TYPE_ADD_SUCCESS,
                data: {},
            });
        else
            response.status(SERVER_ERROR_CODE).json({
                status: 0,
                message: SHIPMENT_TYPE_ADD_FAIL,
                data: {},
            });
    } catch (error) {
        console.log("exports.addShipmentType -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};
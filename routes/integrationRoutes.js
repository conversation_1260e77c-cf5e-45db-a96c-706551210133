const express = require("express");
const router = express();
const adminController = require("../controllers/Admin/commonController");
const commonController = require("../controllers/Admin/commonController");
const companyController = require("../controllers/Admin/companyController");
const shipmentTypeController = require("../controllers/Admin/shipmentTypeController");
const shipmentController = require("../controllers/Admin/shipmentController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const multer = require("multer");
const { errorHandler } = require("../assets/common");
const upload = multer({ dest: "temp/" });



/**
 * @desc Api for generate customer and shipment
 * @method POST
 */

router
    .route("/customer-and-shipment/add")
    .post(
        authentication.validateToken,
        upload.single("photo"),
        validators.admin.addCompany,
        errorHandler,
        companyController.addCompany
    );

/**
* @desc Api for create company Integration Token Active
* @method POST
*/

router
    .route("/company-Integration-Token-Active")
    .post(
        authentication.validateToken,
        validators.admin.companyIdToken,
        errorHandler,
        companyController.companyIntegrationTokenActiveController
    );

/**
* @desc Api for create company Integration Token
* @method POST
*/

router
    .route("/createIntegrationToken")
    .post(
        authentication.validateToken,
        validators.admin.viewCompany,
        errorHandler,
        companyController.createCompanyIntegrationTokenController
    );

/**
* @desc Api for fetch All prepared by user list.
* @method POST
*/

router
    .route("/fetch-preparedList")
    .post(
        authentication.validateToken,
        upload.none(),
        validators.admin.shipmentCheck,
        errorHandler,
        shipmentController.isCheckValidShipmentforItemsController,
        companyController.fetchPreparedListController
    );


module.exports = router;

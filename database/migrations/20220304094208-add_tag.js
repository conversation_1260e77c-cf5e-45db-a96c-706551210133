"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "tag",
        "admin_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "tag",
        "staff_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
   
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("tag", "admin_id");
      await queryInterface.removeColumn("tag", "staff_id");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

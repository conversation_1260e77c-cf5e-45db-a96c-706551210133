# Room ISO Code Update API

This API allows you to update ISO codes for shipment rooms by uploading an Excel file containing room names and their corresponding ISO codes.

## Endpoint

**POST** `/api/admin/room/update-iso-codes-from-excel`

## Authentication

This endpoint requires authentication. Include the authorization token in the request headers.

## Request

### Headers
```
Authorization: Bearer <your_token>
Content-Type: multipart/form-data
```

### Body
- **excel_file** (file, required): Excel file (.xlsx) containing room names and ISO codes

### Excel File Format

The Excel file should have the following structure:

| Column A (Room Name) | Column B (ISO Code) |
|---------------------|---------------------|
| Living Room         | LR001              |
| Kitchen             | KT002              |
| Bedroom             | BR003              |
| Bathroom            | BT004              |

**Notes:**
- First row can be headers (will be skipped)
- Room Name should be in Column A
- ISO Code should be in Column B
- Both columns are required for each row

## Response

### Success Response (200)
```json
{
  "status": 1,
  "message": "Room ISO codes updated successfully",
  "data": {
    "total_excel_rooms": 10,
    "matched_rooms": 8,
    "updated_with_iso_codes": 8,
    "updated_with_999": 5,
    "unmatched_excel_rooms": ["Non-existent Room 1", "Non-existent Room 2"]
  }
}
```

### Error Response (400/500)
```json
{
  "status": 0,
  "message": "Error message",
  "data": {}
}
```

## Response Fields

- **total_excel_rooms**: Total number of rooms found in the Excel file
- **matched_rooms**: Number of rooms that matched with database entries
- **updated_with_iso_codes**: Number of rooms updated with ISO codes from Excel
- **updated_with_999**: Number of database rooms that didn't match Excel entries (set to ISO code 999)
- **unmatched_excel_rooms**: Array of room names from Excel that weren't found in database

## Logic

1. **File Upload**: The API accepts an Excel file upload
2. **Excel Reading**: Reads room names and ISO codes from the Excel file
3. **Database Matching**: Matches room names (case-insensitive) with existing database entries
4. **ISO Code Updates**: 
   - For matched rooms: Updates with ISO codes from Excel
   - For non-matched database rooms: Updates with ISO code "999"
5. **Response**: Returns summary of operations performed

## Example Usage

### Using cURL
```bash
curl -X POST \
  http://your-domain/api/admin/room/update-iso-codes-from-excel \
  -H 'Authorization: Bearer your_token_here' \
  -F 'excel_file=@/path/to/shipment_rooms.xlsx'
```

### Using JavaScript (FormData)
```javascript
const formData = new FormData();
formData.append('excel_file', fileInput.files[0]);

fetch('/api/admin/room/update-iso-codes-from-excel', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

## Error Handling

The API handles various error scenarios:

- **No file uploaded**: Returns error message
- **Invalid Excel file**: Returns error message
- **Empty Excel file**: Returns error message
- **Database errors**: Returns appropriate error message
- **File system errors**: Cleans up uploaded files automatically

## Notes

- The uploaded Excel file is automatically deleted after processing
- Room name matching is case-insensitive
- Only active rooms in the database are considered for updates
- The operation uses database transactions for data integrity
- All database rooms not found in Excel will have their ISO code set to "999"

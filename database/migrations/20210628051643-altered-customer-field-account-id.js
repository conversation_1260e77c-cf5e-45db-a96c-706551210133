"use strict";

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn("customers", "account_id", {
      type: Sequelize.STRING(150),
      allowNull: true,
      defaultValue: ''
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('customers',
      'account_id',
      {
        type: Sequelize.STRING(15),
        allowNull: true,
        defaultValue: ''
      });
  },
};

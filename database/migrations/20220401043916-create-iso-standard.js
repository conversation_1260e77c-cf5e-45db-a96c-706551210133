'use strict';
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('iso_Standards', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ISO_ID: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      ISO_FULL_ID: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      Standard_name: {
        type: Sequelize.STRING
      },
      Standard_Full_Name: {
        type: Sequelize.STRING
      },
      Annex: {
        type: Sequelize.STRING
      },
      level: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      SearchKey: {
        type: Sequelize.STRING
      },
      type: {
        type: Sequelize.STRING
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      }
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('iso_Standards');
  }
};
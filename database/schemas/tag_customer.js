"use strict";
module.exports = (sequelize, DataTypes) => {
  const tag_customer = sequelize.define(
    "tag_customer",
    {
      tag_customer_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tag_id: DataTypes.INTEGER,
      customer_id: DataTypes.INTEGER
    },
    { createdAt: false, updatedAt: false, freezeTableName: true }
  );
  tag_customer.associate = function (models) {
    tag_customer.belongsTo(models.tag,{foreignKey: 'tag_id', as: "m2m_customer_tag"});
    tag_customer.belongsTo(models.customer,{foreignKey: 'customer_id', as: "m2m_customer"});
  };
  return tag_customer;
};

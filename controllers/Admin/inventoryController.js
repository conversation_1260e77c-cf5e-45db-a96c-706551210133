const inventoryModel = require("../../models/Admin/inventoryModel");

exports.listInventory = async (request, response) => {
    try {
        let listInventoryRes = await inventoryModel.listInventory(request)
        response.status(SUCCESS_CODE).json({
            status: 1,
            message: COMPANY_EMAIL_EXIST,
            data: { listInventoryRes },
        });
    } catch (error) {
        console.log("exports.listInventory -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stages',
        'allow_default_qr_code_mode',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'allow_default_qr_code_mode',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_type_stages', 'allow_default_qr_code_mode');
      
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'allow_default_qr_code_mode');

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

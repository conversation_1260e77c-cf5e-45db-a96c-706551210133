"use strict";

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn("shipment_jobs", "job_status", {
      type: Sequelize.INTEGER,
      allowNull: true,
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
      references: {
        model: "shipment_type_stages",
        key: "shipment_stage_id",
      },
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn("shipment_jobs", "job_status", {
      type: Sequelize.ENUM("pending", "pickup", "progress", "delivered"),
      defaultValue: "pending",
    });
  },
};

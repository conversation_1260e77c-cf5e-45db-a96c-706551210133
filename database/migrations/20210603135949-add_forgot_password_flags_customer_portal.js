"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("customers", "passwordTokenExpire", {
				type: Sequelize.DATE,
				defaultValue: null,
				allowNull: true,
			});
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("customers", "passwordTokenExpire");
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

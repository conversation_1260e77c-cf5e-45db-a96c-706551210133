'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    /*
      Add altering commands here.
      Return a promise to correctly handle asynchronicity.

      Example:*/
      return queryInterface.bulkInsert('authentications', [{
        auth_key: 'W4HGelGEiHNxg591vLwJ9CdhAhvuUaBEpakaX27lA',
        created_at: new Date(),
        updated_at: new Date()
      }], {}); 
  },

  down: (queryInterface, Sequelize) => {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:*/
      return queryInterface.bulkDelete('authentications', null, {});
  }
};

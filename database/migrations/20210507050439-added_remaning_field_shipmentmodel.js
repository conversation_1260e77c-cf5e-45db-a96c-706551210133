"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn(
				"shipment_jobs",
				"pickup_address2",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"delivery_address2",
				Sequelize.STRING
			);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("shipment_jobs", "pickup_address2");
			await queryInterface.removeColumn("shipment_jobs", "delivery_address2");
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

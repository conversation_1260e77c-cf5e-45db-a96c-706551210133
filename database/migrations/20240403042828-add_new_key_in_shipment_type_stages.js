"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'total_add_items_inventory_stage',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        },
      );
      await queryInterface.addColumn(
        "shipment_type_stage_for_shipments",
        "total_add_items_storage_stage",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stage_for_shipments",
        "total_remove_items_storage_stage",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stage_for_shipments",
        "total_remove_items_inventory_stage",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_type_stage_for_shipments", "total_add_items_inventory_stage");
      await queryInterface.removeColumn("shipment_type_stage_for_shipments", "total_add_items_storage_stage");
      await queryInterface.removeColumn("shipment_type_stage_for_shipments", "total_remove_items_storage_stage");
      await queryInterface.removeColumn("shipment_type_stage_for_shipments", "total_remove_items_inventory_stage");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
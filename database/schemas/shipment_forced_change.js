"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_job_forced = sequelize.define(
		"shipment_job_forced",
		{
			forced_status_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			reason: DataTypes.STRING,
			shipment_job_id: DataTypes.INTEGER,
			current_stage_id: { type: DataTypes.INTEGER },
			altered_stage_id: { type: DataTypes.INTEGER },
			local_current_stage_id: { type: DataTypes.INTEGER  },
			local_altered_stage_id: { type: DataTypes.INTEGER  },
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			freezeTableName: true,
		}
	);

	shipment_job_forced.associate = function (models) {
		// associations can be defined here
		shipment_job_forced.belongsTo(models.shipment_type_stage, {
			foreignKey: "current_stage_id",
		});
		shipment_job_forced.belongsTo(models.shipment_type_stage, {
			foreignKey: "altered_stage_id",
		});

		shipment_job_forced.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_current_stage_id",
		});
		shipment_job_forced.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_altered_stage_id",
		});

		shipment_job_forced.belongsTo(models.shipment_job, {
			as: "job_id",
			foreignKey: "shipment_job_id",
		});
	};
	return shipment_job_forced;
};

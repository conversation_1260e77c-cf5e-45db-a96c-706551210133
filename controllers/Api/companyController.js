
const companyModelApi = require("../../models/Api/companyModel");
const shipmentModelApi = require("../../models/Api/shipmentModel");
const commonModel = require("../../models/Admin/commonModel");
const commonFunction = require("../../assets/common");
const {
    generateResponse,
} = require("../../assets/common");
const md5 = require('md5');

exports.companyStatusController = async (request, response) => {
    try {
        const responseData = await companyModelApi.companyStatusModel(request.body);
        response.status(SUCCESS_CODE).json({
            status: 1,
            message: "success",
            data: responseData,
        });
    }
    catch (error) {
        console.log("exports.companyStatusController -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

exports.companyKeyValidationForJobId = async (request, response, next) => {
    try {
        const getCompanyId = await companyModelApi.getCompanyIdOfCompanyKey(request.body);
        const jobId = request.params.jobId
            ? request.params.jobId
            : request.body.jobId;
        if (getCompanyId) {
            const companyKeyValidationForJobId = await shipmentModelApi.companyKeyValidationForJobId(jobId, getCompanyId)
            if (companyKeyValidationForJobId) {
                next();
            }
            else {
                response.status(BAD_REQUEST_CODE).json({
                    status: 0,
                    message: "Invalid request data!",
                    data: {},
                });
            }
        }
    }
    catch (error) {
        console.log("exports.getCompanyIdOfCompanyKey -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

exports.companyKeyValidationForShipmentId = async (request, response, next) => {
    try {
        const getCompanyId = await companyModelApi.getCompanyIdOfCompanyKey(request.body);
        const shipmentId = request.params.shipmentId
            ? request.params.shipmentId
            : request.body.shipmentId;
        if (getCompanyId) {
            const companyKeyValidationForShipmentId = await shipmentModelApi.companyKeyValidationForShipmentId(shipmentId, getCompanyId)
            if (companyKeyValidationForShipmentId) {
                next();
            }
            else {
                response.status(BAD_REQUEST_CODE).json({
                    status: 0,
                    message: "Invalid request data!",
                    data: {},
                });
            }
        }
    }
    catch (error) {
        console.log("exports.getCompanyIdOfCompanyKey -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

/**Post /apiKey/add  addCompanyKey Controller */
exports.addCompanyKeyController = async (request, response) => {
    try {
        //here we check api-key exits
        const checkApiKeyExits = await companyModelApi.checkApiKeyExits(request.body);
        if (!checkApiKeyExits) {
            const charsetOptions = await "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            const randomString = await commonFunction.randomString(10, charsetOptions);
            const timeStamp = await String(new Date().getTime());

            //here we create api-key string
            const createCompanyTokenKey = await md5(randomString + timeStamp);

            //here we create api-key
            let createCompanyApiKey = await companyModelApi.createCompanyApiKey(createCompanyTokenKey.toUpperCase(), request.body);

            if (createCompanyApiKey) {
                response.status(SUCCESS_CODE).json({
                    status: 1,
                    message: COMPANY_KEY_ADD_SUCCESS,
                    data: createCompanyApiKey
                });
            } else {
                response.status(SUCCESS_CODE).json({
                    status: 0,
                    message: COMPANY_KEY_ADD_ERROR,
                    data: {},
                });
            }
        }
        else {
            response.status(SUCCESS_CODE).json({
                status: 0,
                message: COMPANY_KEY_EXIST,
                data: {}
            });
        }
    } catch (error) {
        console.log("exports.addCompanyKeyController -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

/**Post /apiKey/list  fetchCompanyKey Controller */
exports.fetchCompanyKeyController = async (request, response) => {
    try {
        //here we fetch api-key
        const listCompanyKey = await companyModelApi.listCompanyKey(request.body);
        if (listCompanyKey)
            generateResponse(
                response,
                SUCCESS_CODE,
                1,
                COMPANY_KEY_RETRIEVED_SUCCESS,
                listCompanyKey
            );
        else
            generateResponse(
                response,
                NOT_FOUND_CODE,
                0,
                COMPANY_KEY_LIST_EMPTY,
                {}
            );
    } catch (error) {
        console.log("exports.fetchCompanyKeyController -> error: ", error);
        generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
    }
}

/**Post /apiKey/delete  deleteCompanyKey Controller */
exports.deleteCompanyKeyController = async (request, response) => {
    try {
        //here we delete api-key
        const deleteCompanyKey = await companyModelApi.deleteCompanyKey(request.body);
        if (deleteCompanyKey) {
            response.status(SUCCESS_CODE).json({
                message: COMPANY_KEY_DELETE_SUCCESS,
            });
        } else {
            response.status(EXPECTATION_FAILED_CODE).json({
                message: COMPANY_KEY_DELETE_FAIL,
                data: {},
            });
        }
    }
    catch (error) {
        console.log("exports.deleteCompanyKeyController -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
}

/**Post /apiKey/change-key-status  changeKeyStatus  Controller */
exports.changeKeyStatusController = async (request, response) => {
    try {
        //here we change status of api-key
        const changeKeyStatusModel = await companyModelApi.changeKeyStatusModel(
            request.body
        );
        if (changeKeyStatusModel) {
            response.status(SUCCESS_CODE).json({
                message: COMPANY_KEY_STATUS_CHANGE,
            });
        }
        else {
            response.status(EXPECTATION_FAILED_CODE).json({
                message: COMPANY_KEY_STATUS_FAIL,
                data: {},
            });
        }
    } catch (error) {
        console.log("exports.changeCompanyStatus -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};


exports.getCompanyKeyController = async (request, response) => {
    try {
        let getUserDetails = await commonModel.getUserDetails(request);
        const fetchCompanyKeyDetails = await companyModelApi.fetchCompanyKeyDetails(getUserDetails)
        if (fetchCompanyKeyDetails)
            commonFunction.generateResponse(response, SUCCESS_CODE, 1, COMPANY_KEY_RETRIEVED_SUCCESS, fetchCompanyKeyDetails)
        else
            commonFunction.generateResponse(response, SUCCESS_CODE, 0, COMPANY_KEY_LIST_EMPTY, { "isEnable": 0, })
    }
    catch (reason) {
        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
    }

}
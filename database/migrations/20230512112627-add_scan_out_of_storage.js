'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.addColumn(
                'shipment_type_stages',
                'scan_out_of_storage',
                {
                    type: Sequelize.BOOLEAN,
                    allowNull: true,
                    defaultValue: 0,
                },
                {
                    logging: true,
                }
            );
            await queryInterface.addColumn(
                'shipment_type_stage_for_shipments',
                'scan_out_of_storage',
                {
                    type: Sequelize.BOOLEAN,
                    allowNull: true,
                    defaultValue: 0,
                },
                {
                    logging: true,
                }
            );
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },

    down: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.removeColumn('shipment_type_stages', 'scan_out_of_storage');
            await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'scan_out_of_storage');
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },
};

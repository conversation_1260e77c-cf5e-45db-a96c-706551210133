"use strict";
module.exports = (sequelize, DataTypes) => {
	const qr_code = sequelize.define(
		"qr_code",
		{
			qr_code_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			job_id: DataTypes.INTEGER,
			company_id: DataTypes.INTEGER,
			batch_id: DataTypes.INTEGER,
			label_number: DataTypes.INTEGER,
			random_number: DataTypes.STRING,
			qr_image: DataTypes.TEXT,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			type: {
				type: DataTypes.ENUM("Shipment", "Generic", "External"),
				defaultValue: "Shipment",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			paranoid: true,
			indexes: [
				{
					unique: true,
					fields: ["random_number"],
				},
			],
		}
	);
	qr_code.associate = function (models) {
		// associations can be defined here
		qr_code.belongsTo(models.shipment_job, { foreignKey: "job_id" });
		qr_code.belongsTo(models.company, { foreignKey: "company_id" });
		qr_code.belongsTo(models.batch_generic_label, { foreignKey: "batch_id" });
		qr_code.hasMany(models.shipment_inventory, {
			foreignKey: "qr_id",
		});
	};
	return qr_code;
};

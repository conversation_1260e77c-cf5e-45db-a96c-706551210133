const { sequelize } = require("../../database/schemas");
const companyModel = require("../../models/Admin/companyModel");
const staffModel = require("../../models/Admin/staffModel");
const commonModel = require("../../models/Admin/commonModel");
// const openApiModel = require("../../models/Api/customerModel");
const commonFunction = require("../../assets/common");
const customerModel = require("../../models/Admin/customerModel");

const AWS = require("aws-sdk");
const fs = require("fs");
const md5 = require('md5');
const axios = require("axios");


exports.fetchNewCompanyUnits = async (request, response) => {
	try {
		const { unitList } = request.body;
		const unitDetails = await companyModel.fetchNewCompanyUnits(unitList);
		if (unitDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				"success",
				unitDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"fail",
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}


exports.addCompanyUnits = async (request, response) => {
	try {
		const { unitList } = request.body;
		const unitDetails = await companyModel.createUnitModels(unitList);
		if (unitDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				"success",
				unitDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				"fail",
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}


exports.checkCompanyAccountIdMandatory = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const companyList = await companyModel.checkCompanyAccountIdMandatory(getUserDetails)
		if (companyList) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: COMPANY_RETRIEVED_SUCCESS,
				data: companyList,
			});
		}
	}
	catch (error) {
		console.log("exports.checkCompanyAccountIdMandatory -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.getCompanyList = async (request, response) => {
	try {
		const companyList = await companyModel.getCompanyList(
			request.company_id,
			request.body
		);
		if (companyList !== "") {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: COMPANY_RETRIEVED_SUCCESS,
				data: { companyList },
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: COMPANY_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.getCompanyList -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.companyIDBasicController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		companyIDBasicData = await companyModel.companyIDBasicController(request.query)
		if (companyIDBasicData && companyIDBasicData.length > 0)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				COMPANY_RETRIEVED_SUCCESS,
				companyIDBasicData
			);
		else commonFunction.generateResponse(response, NOT_FOUND_CODE, 0, COMPANY_NOT_FOUND, {});
	}
	catch (reason) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
};

exports.consumerLoginJsonFun = async (request, response, integration_key) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}

exports.companyStatusJson = async (request, response, fetchCompanyForStorage, consumerLoginJson) => {
	const companyId = fetchCompanyForStorage.storage_company_id
	const companyStatusJson = JSON.stringify({
		isDeleted: false,
		isActive: fetchCompanyForStorage.status == "active" ? 1 : 0
	});
	try {
		const companyStatusResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies/active-status/${companyId}`, companyStatusJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (companyStatusResponse.status == 200) {
			return companyStatusResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Company status fail 1", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Company status fail 2", {});
	}
}

exports.changeCompanyStatus = async (request, response) => {
	try {
		const companyDetail = await companyModel.changeCompanyStatus(
			request.body
		);
		if (companyDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				message: STATUS_CHANGE_SUCCESS,
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeCompanyStatus -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.changeCompanyStatusStorage = async (request, response) => {
	try {
		const companyDetail = await companyModel.changeCompanyStatusStorage(
			request.body
		);
		const staffDetail = await staffModel.changeStaffStatusStorage(request.body);

		if (companyDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: STATUS_CHANGE_SUCCESS,
				data: {},
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeCompanyStatusStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};


exports.fetchExistingCompanyCustomerData = async (request, response) => {
	try {
		const fetchExistingCompanyCustomerData = await companyModel.fetchExistingCompanyCustomerData(request.body);
		commonFunction.generateResponse(response, SUCCESS_CODE, 1, "Customer data found", fetchExistingCompanyCustomerData)

	} catch (error) {
		console.log("exports.createExistingCompanyData -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.fetchExistingCompanyStaffData = async (request, response) => {
	try {
		const fetchExistingCompanyStaffData = await companyModel.fetchExistingCompanyStaffData(request.body);
		commonFunction.generateResponse(response, SUCCESS_CODE, 1, "Staff data found.", fetchExistingCompanyStaffData)

	} catch (error) {
		console.log("exports.createExistingCompanyData -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}




exports.changeIntegrationKeyStatusController = async (request, response) => {
	try {
		//here we change status of integration_key
		const changeIntegrationKeyStatusController = await companyModel.changeIntegrationKeyStatusController(
			request.body
		);

		if (changeIntegrationKeyStatusController.includes(1)) {
			const fetchCompanyKeyDetails = await companyModel.getIntegrationKeyData(request.body)
			response.status(SUCCESS_CODE).json({ message: COMPANY_INTEGRATION_KEY_STATUS_CHANGE, data: fetchCompanyKeyDetails });
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({ message: COMPANY_INTEGRATION_KEY_STATUS_FAIL, data: {} });
		}
	} catch (error) {
		console.log("exports.changeIntegrationKeyStatusController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({ status: 0, message: error.message, data: {}, });
	}
};

exports.viewCompany = async (request, response) => {
	try {
		let id = request.body.company_id
			? request.body.company_id
			: request.params.companyId;

		const companyDetail = await companyModel.viewCompany(
			request.company_id,
			id
		);
		if (companyDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: COMPANY_RETRIEVED_SUCCESS,
				data: companyDetail,
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: COMPANY_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.companyStorageIdUpdate = async (request, response) => {
	try {
		const addInterigationKey = await companyModel.addInterigationKey(
			request.body
		);

		const companyDetail = await companyModel.companyStorageIdUpdate(request.body);
		if (companyDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "success",
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "fail",
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.companyStorageIdUpdate -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.fetchPreparedListController = async (request, response) => {
	try {
		let PreparedList = await companyModel.PreparedListModel(request.body);
		if (PreparedList) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: STAFF_RETRIEVED_SUCCESS,
				data: PreparedList
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: STAFF_NOT_FOUND,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.fetchPreparedListController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.createCompanyIntegrationTokenController = async (request, response) => {
	try {
		const fetchCompanyIntegrationToken = await companyModel.fetchCompanyIntegrationToken(request.body)
		const addInterigationKey = await companyModel.addInterigationKey2(
			request.body,
			fetchCompanyIntegrationToken
		);

		if (addInterigationKey) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "Interigation token created successfully",
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "Interigation token created fail",
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.createCompanyIntegrationTokenController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.fetchCustomerInStorage = async (request, response, CustomerData, consumerLoginJson) => {
	try {
		const data = JSON.stringify({
			email: [
				CustomerData.email !== undefined ? CustomerData.email : "",
				CustomerData.email2 !== undefined ? CustomerData.email2 : "",
			]
		})
		const fetchCustomerInStorageResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/check-customer-email-already-exist`, data,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': consumerLoginJson.data.accessToken,
				}
			})
		if (fetchCustomerInStorageResponse) {
			if (fetchCustomerInStorageResponse.data.data) {
				return false
			}
			else {
				return true
			}
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
		}

	} catch (error) {
		console.log("error", error)
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
	}
}

exports.addCustomerJson = async (request, response, CustomerData, consumerLoginJson) => {
	const addCustomerJson = JSON.stringify({
		importCustomerId: CustomerData.customer_id,
		firstName: (CustomerData.first_name !== undefined) ? CustomerData.first_name : "",
		lastName: (CustomerData.last_name !== undefined) ? CustomerData.last_name : "",
		address: {
			addressLine1: (CustomerData.address1 !== undefined) ? CustomerData.address1 : "",
			addressLine2: (CustomerData.address2 !== undefined) ? CustomerData.address2 : "",
			city: (CustomerData.city !== undefined) ? CustomerData.city : "",
			state: (CustomerData.state !== undefined) ? CustomerData.state : "",
			zipcode: (CustomerData.zipCode !== undefined) ? CustomerData.zipCode : "",
			country: (CustomerData.country !== undefined) ? CustomerData.country : "",
		},
		email: [
			(CustomerData.email !== undefined) ? CustomerData.email : "",
			(CustomerData.email2 !== undefined) ? CustomerData.email2 : "",
		],
		phoneNumber: [
			(CustomerData.phone !== undefined) ? CustomerData.phone : "",
			(CustomerData.phone2 !== undefined) ? CustomerData.phone2 : "",
			(CustomerData.phone3 !== undefined) ? CustomerData.phone3 : "",
		],
		accountId: (CustomerData.account_id !== undefined) ? CustomerData.account_id : "",
		accountName: (CustomerData.account_name !== undefined) ? CustomerData.account_name : "",
		salesRep: (CustomerData.sales_rep !== undefined) ? CustomerData.sales_rep : "",
		companyId: request.body.id,
		importedTags: [

		],
		moverInventoryCustomerId: CustomerData.customer_id,
		createdFromMoverInventory: true,
	})

	try {
		const addCustomerResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, addCustomerJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (addCustomerResponse) {
			// return addCustomerResponse.data;
			let params = {
				customer_id: CustomerData.customer_id,
				customerIdStorage: addCustomerResponse.data.data.id
			};

			const customerDetail = await customerModel.customerStorageIdUpdate(params);
			return customerDetail
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
	}
}


exports.companyIntegrationTokenActiveController = async (request, response) => {
	try {
		const { unitList } = request.body;


		//here we find company details
		const findCompanyIdTokenMoverInventory = await companyModel.findCompanyIdTokenMoverInventory(request.body);
		//here we change status of integration_key
		const changeIntegrationKeyStatusController = await companyModel.updateIntegrationKeyStatusController(request.body);
		//here we updating storage company Id
		const companyStorageIdTokenMoverInventoryUpdate = await companyModel.companyStorageIdTokenMoverInventoryUpdate(request.body, findCompanyIdTokenMoverInventory)
		const fetchExistingCompanyCustomerData = await companyModel.fetchExistingStorageCompanyCustomerData(findCompanyIdTokenMoverInventory);

		const consumerLoginJson = await this.consumerLoginJsonFun(request, response, request.body.companyIdTokenMoverInventory);
		if (fetchExistingCompanyCustomerData.count > 0) {
			for (let i = 0; i < fetchExistingCompanyCustomerData.rows.length; i++) {
				const fetchCustomerInStorage = await this.fetchCustomerInStorage(request, response, fetchExistingCompanyCustomerData.rows[i], consumerLoginJson)
				if (fetchCustomerInStorage) {
					const addCustomerJson = await this.addCustomerJson(request, response, fetchExistingCompanyCustomerData.rows[i], consumerLoginJson)
				}
			}
		}

		const unitDetails = await companyModel.fetchNewCompanyUnits(unitList);

		if (changeIntegrationKeyStatusController.includes(1)) {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: COMPANY_INTEGRATION_KEY_STATUS_CHANGE,
				data: {},
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: COMPANY_INTEGRATION_KEY_STATUS_FAIL,
				data: {},
			});
		}


	} catch (error) {
		console.log("exports.companyIntegrationTokenActiveController -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.addCompany = async (request, response) => {
	try {
		const identityCount = await companyModel.checkExistingIdentity(
			request.body.company_identity
		);
		if (identityCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: COMPANY_IDENTITY_EXIST,
				data: {},
			});
		}
		else {
			const emailCount = await companyModel.checkExistingEmail(
				request.body.email
			);
			if (emailCount > 0) {
				response.status(CONFLICT_CODE).json({
					status: 1,
					message: COMPANY_EMAIL_EXIST,
					data: {},
				});
			}
			else {
				try {
					let newFileName = "";
					let headerData = await commonFunction.jwtTokenDecode(
						request.headers.access_token
					);
					if (request.file && request.file.originalname !== "") {
						const fileName = request.file.filename;
						const fileExt = request.file.originalname.split(".");
						newFileName = fileName + "." + fileExt[1];
						const s3 = new AWS.S3();
						let params = {
							ACL: "public-read",
							Bucket: Const_AWS_BUCKET,
							Body: fs.createReadStream(request.file.path),
							Key: Const_AWS_Company_Profile + "original/" + newFileName,
						};
						let result = await commonFunction.UploadImageS3(params, s3);
						if (result) {
							fs.unlinkSync(request.file.path);
						}
					}
					let html = await commonFunction.readFile("welcome.html");
					html = html.replace(/{{name}}/g, request.body.company_name);
					html = html.replace(/{{email}}/g, request.body.email);
					html = html.replace(/{{password}}/g, request.body.password);
					html = html.replace(/{{cmsUrl}}/g, CMS_URL_LINK);

					let isEmailSent = await commonFunction.sendEmail(
						request.body.email,
						"Welcome to Mover Inventory",
						html
					);
					if (isEmailSent === false) {
						throw Error("Error in mail send !");
					} else {
						try {
							const companyDetail = await companyModel.addCompany(
								request.body,
								newFileName,
							);

							response.status(SUCCESS_CODE).json({
								status: 1,
								message: COMPANY_ADD_SUCCESS,
								data: companyDetail,
							});
						} catch (error) {
							response.status(SERVER_ERROR_CODE).json({
								status: 0,
								message: error.message,
								data: {},
							});
						}
					}
				} catch (error) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: COMPANY_ADD_FAIL,
						data: {},
					});
				}
			}
		}
	} catch (error) {
		console.log("exports.addCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editCompany = async (request, response) => {
	try {
		const identityCount = await companyModel.checkReExistingIdentity(
			request.body
		);
		if (identityCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: COMPANY_IDENTITY_EXIST,
				data: {},
			});
		}
		else {
			const emailCount = await companyModel.checkReExistingEmail(
				request.body
			);
			if (emailCount > 0) {
				response.status(CONFLICT_CODE).json({
					status: 1,
					message: COMPANY_EMAIL_EXIST,
					data: {},
				});
			} else {
				try {
					let newFileName = "";
					let headerData = await commonFunction.jwtTokenDecode(
						request.headers.access_token
					);
					if (request.file && request.file.originalname !== "") {
						const oldCompanyData = await companyModel.viewCompany(
							request.company_id,
							request.body.company_id
						);
						const fileName = request.file.filename;
						const fileExt = request.file.originalname.split(".");
						newFileName = fileName + "." + fileExt[1];
						const s3 = new AWS.S3();
						let params = {
							ACL: "public-read",
							Bucket: Const_AWS_BUCKET,
							Body: fs.createReadStream(request.file.path),
							Key: Const_AWS_Company_Profile + "original/" + newFileName,
						};
						if (oldCompanyData.photo) {
							let delMedia = {
								Bucket: Const_AWS_BUCKET,
								Key:
									Const_AWS_Company_Profile +
									"original/" +
									oldCompanyData.photo,
							};
							await commonFunction.deleteImageS3(delMedia, s3);
						}
						let result = await commonFunction.UploadImageS3(params, s3);
						if (result) {
							fs.unlinkSync(request.file.path);
						}
					}
					const companyDetail = await companyModel.editCompany(
						request.body,
						newFileName,
					);
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: COMPANY_UPDATE_SUCCESS,
						data: {},
					});
				} catch (error) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: COMPANY_UPDATE_FAIL,
						data: {},
					});
				}
			}
		}
	} catch (error) {
		console.log("exports.editCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editCompanyStorage = async (request, response) => {
	try {

		const getCompanyStorageIdEdit = await companyModel.getCompanyIdForStaff(request.body);
		request.body.company_id = getCompanyStorageIdEdit.company_id

		const identityCount = await companyModel.checkReExistingIdentity(
			request.body
		);

		if (identityCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: COMPANY_IDENTITY_EXIST,
				data: {},
			});
		}
		else {
			const emailCount = await companyModel.checkReExistingEmail(
				request.body
			);
			if (emailCount > 0) {
				response.status(CONFLICT_CODE).json({
					status: 1,
					message: COMPANY_EMAIL_EXIST,
					data: {},
				});
			} else {
				try {
					let newFileName = "";
					let headerData = await commonFunction.jwtTokenDecode(
						request.headers.access_token
					);
					if (request.file && request.file.originalname !== "") {
						const oldCompanyData = await companyModel.viewCompany(
							request.company_id,
							request.body.company_id
						);
						const fileName = request.file.filename;
						const fileExt = request.file.originalname.split(".");
						newFileName = fileName + "." + fileExt[1];
						const s3 = new AWS.S3();
						let params = {
							ACL: "public-read",
							Bucket: Const_AWS_BUCKET,
							Body: fs.createReadStream(request.file.path),
							Key: Const_AWS_Company_Profile + "original/" + newFileName,
						};
						if (oldCompanyData.photo) {
							let delMedia = {
								Bucket: Const_AWS_BUCKET,
								Key:
									Const_AWS_Company_Profile +
									"original/" +
									oldCompanyData.photo,
							};
							await commonFunction.deleteImageS3(delMedia, s3);
						}
						let result = await commonFunction.UploadImageS3(params, s3);
						if (result) {
							fs.unlinkSync(request.file.path);
						}
					}
					const companyDetail = await companyModel.editCompany(
						request.body,
						newFileName,
					);
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: COMPANY_UPDATE_SUCCESS,
						data: {},
					});
				} catch (error) {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: COMPANY_UPDATE_FAIL,
						data: {},
					});
				}
			}
		}
	} catch (error) {
		console.log("exports.editCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.companyDeleteJson = async (request, response, fetchCompanyForStorage, consumerLoginJson) => {
	const companyId = fetchCompanyForStorage.storage_company_id
	const companyDeleteJson = JSON.stringify({
		isDeleted: true,
		isActive: 0
	});
	try {
		const companyDeleteResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies/active-status/${companyId}`, companyDeleteJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (companyDeleteResponse.status == 200) {
			return companyDeleteResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Company delete fail ", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Company delete fail 2", {});
	}
}

exports.deleteCompany = async (request, response) => {
	try {

		const companyAllUserSoftDelete = await companyModel.companyAllUserSoftDelete(request.body);
		const companyAllCustomerSoftDelete = await companyModel.companyAllCustomerSoftDelete(request.body);
		const companyDetail = await companyModel.deleteCompany(request.body);

		if (companyDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				message: COMPANY_DELETE_SUCCESS,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: COMPANY_DELETE_FAIL,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.deleteCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.isValidCompanyController = async (request, response, next) => {

	const check = await companyModel.isValidCompanyModel(request.body.company_id)
	if (check) {
		next();
	}
	else {
		commonFunction.generateResponse(
			response,
			NOT_FOUND_CODE,
			0,
			COMPANY_NOT_FOUND,
			{}
		);
	}
};


exports.getIntegrationKeyController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const fetchCompanyKeyDetails = await companyModel.getIntegrationKeyController(getUserDetails)
		if (fetchCompanyKeyDetails)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, COMPANY_KEY_RETRIEVED_SUCCESS, fetchCompanyKeyDetails)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, COMPANY_KEY_LIST_EMPTY, {})
	}
	catch (reason) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}

}

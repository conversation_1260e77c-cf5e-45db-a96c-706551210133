// Script to examine the shipment_rooms Excel file structure
const ExcelJS = require("exceljs");
const fs = require("fs");

async function examineShipmentRoomsExcel() {
    try {
        const filePath = "./docs/shipment_rooms.xlsx";
        
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            console.log("❌ File not found:", filePath);
            return;
        }
        
        console.log("📁 Reading Shipment Rooms Excel file:", filePath);
        console.log("📊 File size:", fs.statSync(filePath).size, "bytes");
        
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        console.log("📋 Number of worksheets:", workbook.worksheets.length);
        
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            console.log("❌ No worksheet found");
            return;
        }
        
        console.log("📄 Worksheet name:", worksheet.name || "Sheet1");
        console.log("📏 Dimensions:", `${worksheet.rowCount} rows x ${worksheet.columnCount} columns`);
        
        console.log("\n📋 First 15 rows:");
        console.log("Row | Column A                  | Column B        | Column C        | Column D");
        console.log("-".repeat(85));
        
        let validDataCount = 0;
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber <= 15) {
                const cellA = row.getCell(1).value;
                const cellB = row.getCell(2).value;
                const cellC = row.getCell(3).value;
                const cellD = row.getCell(4).value;
                
                const colA = cellA ? String(cellA).substring(0, 25) : '';
                const colB = cellB ? String(cellB).substring(0, 15) : '';
                const colC = cellC ? String(cellC).substring(0, 15) : '';
                const colD = cellD ? String(cellD).substring(0, 15) : '';
                
                console.log(`${rowNumber.toString().padStart(3)} | ${colA.padEnd(25)} | ${colB.padEnd(15)} | ${colC.padEnd(15)} | ${colD}`);
                
                // Count valid data (skip header row)
                if (rowNumber > 1 && cellA) {
                    validDataCount++;
                }
            }
        });
        
        console.log("\n📊 Summary:");
        console.log(`   - Total rows: ${worksheet.rowCount}`);
        console.log(`   - Valid data rows (excluding header): ${validDataCount}`);
        
        // Show column headers
        console.log("\n📋 Column Headers:");
        const headerRow = worksheet.getRow(1);
        for (let i = 1; i <= worksheet.columnCount; i++) {
            const headerValue = headerRow.getCell(i).value;
            if (headerValue) {
                console.log(`   Column ${String.fromCharCode(64 + i)}: ${headerValue}`);
            }
        }
        
        // Show some sample data for verification
        console.log("\n🔍 Sample data entries:");
        let sampleCount = 0;
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1 && sampleCount < 5) {
                const rowData = [];
                for (let i = 1; i <= worksheet.columnCount; i++) {
                    const cellValue = row.getCell(i).value;
                    if (cellValue) {
                        rowData.push(`Col${String.fromCharCode(64 + i)}: "${String(cellValue).trim()}"`);
                    }
                }
                if (rowData.length > 0) {
                    console.log(`   ${sampleCount + 1}. ${rowData.join(', ')}`);
                    sampleCount++;
                }
            }
        });
        
        console.log("\n✅ Shipment rooms Excel file examination complete!");
        
    } catch (error) {
        console.error("❌ Error examining Excel file:", error.message);
    }
}

examineShipmentRoomsExcel();


const {
    generateResponse,
} = require("../../assets/common");
const qrCodeModelApi = require("../../models/Api/qrCodeModel");


/**Post /qr_code/:jobId/list listQrCodeController Controller */
exports.listQrCodeController = async (request, response) => {
    try {
        const { jobId } = request.params;
        //here we fetch qrCodeList
        const qrCodeList = await qrCodeModelApi.listQrCodeModel(request.query,jobId);
        generateResponse(response, SUCCESS_CODE, 1, GENERIC_REQUEST_SUCCESS, qrCodeList);
    } catch (error) {
        console.log("exports.listQrCode -> error: ", error);
        generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
    }
};
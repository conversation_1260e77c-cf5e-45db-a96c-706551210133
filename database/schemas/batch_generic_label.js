"use strict";
module.exports = (sequelize, DataTypes) => {
  const batch_generic_label = sequelize.define(
    "batch_generic_label",
    {
      batch_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: DataTypes.INTEGER,
      batch_number: DataTypes.INTEGER,
      date: {
        type: DataTypes.DATE,
      },
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    {
      createdAt: false,
      updatedAt: false,
    }
  );
  batch_generic_label.associate = function (models) {
    batch_generic_label.belongsTo(models.company, { foreignKey: "company_id" });
    batch_generic_label.hasMany(models.qr_code, {
      as:"batches",
			foreignKey:"batch_id",
		});
  };
  return batch_generic_label;
};

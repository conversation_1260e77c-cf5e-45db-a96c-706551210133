const express = require("express");
const router = express();

//Api
const customerControllerApi = require("../controllers/Api/customerController");
const shipmentTypeControllerApi = require("../controllers/Api/shipmentTypeController");
const tagControllerApi = require("../controllers/Api/tagController");
const shipmentControllerApi = require("../controllers/Api/shipmentController");
const staffControllerApi = require("../controllers/Api/staffController");
const companyControllerApi = require("../controllers/Api/companyController");
const qrControllerApi = require("../controllers/Api/qrController");
const roomControllerApi = require("../controllers/Api/roomController");
const itemSuggestionControllerApi = require("../controllers/Api/itemSuggestionController");
// const homeControllerApi = require("../controllers/Api/homeController");

//Admin
const customerController = require("../controllers/Admin/customerController");
const companyController = require("../controllers/Admin/companyController");
const shipmentTypeController = require("../controllers/Admin/shipmentTypeController");
const shipmentController = require("../controllers/Admin/shipmentController");
const qrController = require("../controllers/Admin/qrCodeController");
const staffController = require("../controllers/Admin/staffController");
const itemSuggestionController = require("../controllers/Admin/itemSuggestionController");
const homeController = require("../controllers/APP/homeController");


const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const multer = require("multer");
const { errorHandler } = require("../assets/common");
const upload = multer({ dest: "temp/" });


/**
 * @desc Api for generate company Api-key.
 * @method POST
 */

router
    .route("/apiKey/add")
    .post(
        upload.none(),
        authentication.validateToken,
        validators.openApi.apiKeyValidators,
        errorHandler,
        companyController.isValidCompanyController,
        companyControllerApi.addCompanyKeyController
    );

/**
* @desc Api for fetch company Api-key for user and company.
* @method POST
*/

router
    .route("/apiKey/fetch")
    .get(
        upload.none(),
        authentication.validateToken,
        companyControllerApi.getCompanyKeyController
    );

/**
* @desc Api for listing company Api-key.
* @method POST
*/

router
    .route("/apiKey/list")
    .post(
        upload.none(),
        authentication.validateToken,
        validators.openApi.apiKeyValidators,
        errorHandler,
        companyController.isValidCompanyController,
        companyControllerApi.fetchCompanyKeyController
    );

/**
* @desc Api for change company Api-key status.
* @method POST
*/

router
    .route("/apiKey/change-key-status")
    .post(
        upload.none(),
        authentication.validateToken,
        validators.openApi.apiKeyValidators,
        errorHandler,
        companyController.isValidCompanyController,
        companyControllerApi.changeKeyStatusController
    );


/**
* @desc Api for delete company Api-key.
* @method POST
*/

router
    .route("/apiKey/delete")
    .post(
        upload.none(),
        authentication.validateToken,
        validators.openApi.apiKeyValidators,
        errorHandler,
        companyControllerApi.deleteCompanyKeyController
    );

/**
 * @desc open-Api for generate company customers.
 * @method POST
 */

router
    .route("/customer/add")
    .post(
        upload.single("photo"),
        validators.openApi.addCustomer,
        errorHandler,
        authentication.validateCompanyKey,
        companyController.isValidCompanyController,
        customerControllerApi.addCustomer
    );

/**
* @desc open-Api for get warehouseList.
* @method POST
*/

router
    .route("/company/warehouseList")
    .post(
        upload.none(),
        authentication.validateCompanyKey,
        companyController.isValidCompanyController,
        shipmentControllerApi.fetchWarehouseList
    );

/**
 * @desc open-Api for generate company shipments.
 * @method POST
 */

router
    .route("/shipment/")
    .post(
        upload.none(),
        validators.openApi.shipmentCreate,
        validators.openApi.viewCustomer,
        errorHandler,
        authentication.validateCompanyKey,
        customerController.isValidCustomerController,
        shipmentTypeController.isValidShipmentTypeController,
        companyController.isValidCompanyController,
        shipmentControllerApi.createShipmentController
    );

/**
* @desc open-Api for company customers listing.
* @method POST
*/

router
    .route("/customer/list")
    .post(
        upload.none(),
        validators.openApi.getCustomerList,
        errorHandler,
        authentication.validateCompanyKey,
        customerControllerApi.getCustomerList
    );

/**
* @desc open-Api for view customer details.
* @method POST
*/

router
    .route("/customer/view-customer")
    .post(
        upload.none(),
        validators.openApi.viewCustomer,
        validators.openApi.companyKey,
        errorHandler,
        authentication.validateCompanyKey,
        customerControllerApi.viewCustomer
    );
/**
* @desc open-Api for company shipment-types listing.
* @method POST
*/

router
    .route("/shipment-type/list")
    .post(
        upload.none(),
        validators.openApi.shipmentTypeList,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentTypeControllerApi.listShipmentType
    );

/**
* @desc open-Api for add shipment type stgae.
* @method POST
*/

router
    .route("/shipment-type-stage/add")
    .post(
        upload.none(),
        validators.openApi.addShipmentTypeStage,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentTypeControllerApi.addShipmentTypeStage
    );

/**
* @desc Api for view shipment-type-for-shipment stage.
* @method POST
*/

router
    .route("/shipment-type-for-shipment/edit-shipment-stage")
    .post(
        upload.none(),
        validators.openApi.editShipmentStageForShipment,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentTypeControllerApi.editShipmentStage
    );

/**
* @desc Api for view shipment-type-for-shipment stage.
* @method POST
*/

router
    .route("/shipment-type-for-shipment/view-shipment-stage-list")
    .post(
        upload.none(),
        validators.openApi.viewShipmentStageForShipment,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentTypeControllerApi.viewShipmentStageForShipment
    );


/**
* @desc open-Api for generate company tags.
* @method POST
*/

router
    .route("/tag/")
    .post(
        upload.none(),
        validators.openApi.tagAdd,
        errorHandler,
        authentication.validateCompanyKey,
        tagControllerApi.createTagController
    );

/**
* @desc open-Api for company shipments listing.
* @method POST
*/

router
    .route("/shipment/list")
    .post(
        upload.none(),
        validators.openApi.shipmentList,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentControllerApi.listShipmentController
    );



/**
* @desc open-Api for shipment details using shipmentId.
* @method POST
*/

router
    .route("/shipment-details/:shipmentId")
    .post(
        upload.none(),
        validators.openApi.shipmentFetchDetail,
        validators.admin.orderingField,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentController.isValidShipmentController,
        shipmentControllerApi.getShipmentDetailController
    );


/**
* @desc open-Api for company staffs listing.
* @method POST
*/

router
    .route("/staff/list")
    .post(
        upload.none(),
        validators.openApi.staffList,
        errorHandler,
        authentication.validateCompanyKey,
        staffControllerApi.staffListController
    );


/**
* @desc open-Api for customer portal invite.
* @method POST
*/

router
    .route("/mail_customer")
    .post(
        upload.none(),
        validators.openApi.shipmentFetchDetail,
        // errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentControllerApi.isValidShipmentController,
        customerControllerApi.mailShipmentDetailToCustomerController,
        customerController.updateCustomerMailStatusController
    );

/**
* @desc open-Api for generate company tags.
* @method POST
*/

router
    .route("/qr_code/generate")
    .post(
        upload.none(),
        validators.openApi.qrCodeGenerate,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForJobId,
        qrController.checkJobExistence,
        qrController.generateQrController
    );

/**
* @desc open-Api for assign(link) workers to shipment.
* @method PUT
*/


router
    .route("/shipment/:shipmentId/link/")
    .put(
        upload.none(),
        validators.openApi.shipmentFetchDetail,
        validators.openApi.linkWorkers,
        errorHandler,
        authentication.validateCompanyKey,
        staffController.isValidStaffIdController,
        shipmentController.isValidShipmentController,
        shipmentController.staffAssignToShipmentStage
    );

/**
* @desc open-Api for unlink workers to shipment.
* @method PUT
*/

router
    .route("/shipment/:shipmentId/unlink/")
    .put(
        upload.none(),
        validators.openApi.shipmentFetchDetail,
        validators.openApi.unlinkWorkers,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentController.isValidShipmentController,
        staffController.removeStaffIdController

    );

/**
* @desc Api for get staff list using shipmentId.
* @method POST
*/

router
    .route("/staff/list/:shipmentId")
    .post(
        validators.openApi.shipmentFetchDetail,
        errorHandler,
        authentication.validateCompanyKey,
        staffController.staffListByShipmentController
    );

/**
* @desc open-Api for company tags listing.
* @method POST
*/

router
    .route("/tag/list")
    .post(
        upload.none(),
        validators.openApi.tagListData,
        errorHandler,
        authentication.validateCompanyKey,
        tagControllerApi.getTagListingController
    );


/**
* @desc open-Api for customer tags listing.
* @method POST
*/

router
    .route("/tag/list/customer")
    .post(
        upload.none(),
        validators.openApi.companyKey,
        errorHandler,
        authentication.validateCompanyKey,
        tagControllerApi.getTagForCustomerListingController
    );

/**
* @desc open-Api for shipment tags listing.
* @method POST
*/

router
    .route("/tag/list/shipment")
    .post(
        upload.none(),
        validators.openApi.companyKey,
        errorHandler,
        authentication.validateCompanyKey,
        tagControllerApi.getTagForShipmentListingController
    );

/**
* @desc open-Api for customer tags listing.
* @method POST
*/

router
    .route("/qr_code/:jobId/list")
    .post(
        upload.none(),
        validators.openApi.qrCodeList,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForJobId,
        qrController.checkJobExistence,
        qrControllerApi.listQrCodeController
    );


/**
* @desc open-Api for generate room.
* @method POST
*/
router
    .route("/room/")
    .post(
        upload.none(),
        validators.openApi.roomName,
        errorHandler,
        authentication.validateCompanyKey,
        roomControllerApi.createRoomController
    );

/**
* @desc open-Api for generate item.
* @method POST
*/

router
    .route("/item_suggestion/")
    .post(
        upload.none(),
        validators.openApi.itemName,
        errorHandler,
        authentication.validateCompanyKey,
        itemSuggestionControllerApi.createItemSuggestionController
    );

/**
* @desc open-Api for update shipment using shipmentId.
* @method PUT
*/

router
    .route("/shipment/:shipmentId")
    .put(
        upload.none(),
        validators.openApi.shipmentUpdate,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentController.isValidShipmentController,
        shipmentControllerApi.updateShipmentController
    );


/**
* @desc open-Api for edit customer.
* @method POST
*/

router
    .route("/customer/edit-customer")
    .post(
        upload.none(),
        validators.openApi.editCustomer,
        errorHandler,
        authentication.validateCompanyKey,
        customerController.isValidCustomerController,
        customerControllerApi.editCustomer
    );

/**
* @desc open-Api for fetch room list.
* @method POST
*/

router
    .route("/room/list/")
    .post(
        upload.none(),
        validators.openApi.roomList,
        errorHandler,
        authentication.validateCompanyKey,
        roomControllerApi.getRoomListingController
    );

/**
* @desc open-Api for fetch itemlist.
* @method POST
*/

router
    .route("/item_suggestion/list/")
    .post(
        upload.none(),
        validators.openApi.itemList,
        errorHandler,
        authentication.validateCompanyKey,
        itemSuggestionControllerApi.getItemSuggestionListingController
    );


/**
* @desc open-Api for gererate shipment type.
* @method POST
*/

// router
//     .route("/shipment-type/add")
//     .post(
//         validators.openApi.addShipmentType,
//         errorHandler,
//         authentication.validateCompanyKey,
//         shipmentTypeControllerApi.addShipmentType
//     );


/**
* @desc open-Api for fetch all itemList using shipmentId.
* @method POST
*/

router.route("/allItemsList/:shipmentId")
    .post(
        upload.none(),
        validators.openApi.companyKey,
        validators.openApi.allItemsListValidation,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentController.isCheckValidShipmentController,
        homeController.allItemsListController,
    );

/**
* @desc open-Api for delete shipment using shipmentId.
* @method DELETE
*/

router
    .route("/shipment")
    .delete(
        upload.none(),
        validators.openApi.shipmentDelete,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentControllerApi.isValidShipmentController,
        shipmentControllerApi.checkShipmentAssignToJob,
        shipmentControllerApi.removeShipmentController
    );


/**
* @desc open-Api for get shipment details pdf.
* @method POST
*/

router
    .route("/shipment/pdf")
    .post(
        upload.none(),
        validators.openApi.shipmentDelete,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyKeyValidationForShipmentId,
        shipmentController.isCheckValidShipmentforItemsController,
        shipmentControllerApi.downloadPdfController
    );

/**
* @desc open-Api for get unit type list using warehouseId.
* @method POST
*/

router
    .route("/unit-type/warehouse")
    .post(
        upload.none(),
        validators.openApi.unitTypeList,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentControllerApi.validWarehouseIdController,
        shipmentControllerApi.unitTypeList
    );

/**
* @desc open-Api for create complete shipment process with customer, customer portal invite, shipment labels and assign worker 
* @method POST
*/

router
    .route("/shipmentAndCustomer/create")
    .post(
        upload.none(),
        validators.openApi.shipmentAndCustomerController,
        errorHandler,
        authentication.validateCompanyKey,
        shipmentTypeController.isValidShipmentTypeController,
        shipmentControllerApi.shipmentAndCustomerController,
    );

/**
* @desc open-Api for check company status
* @method POST
*/

router
    .route("/company-status")
    .post(
        upload.none(),
        validators.openApi.companyKey,
        errorHandler,
        authentication.validateCompanyKey,
        companyControllerApi.companyStatusController,
    );


module.exports = router;




"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_jobs",
        "contact_reference",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      await queryInterface.addColumn(
        "shipment_jobs",
        "account_reference",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      await queryInterface.addColumn(
        "shipment_jobs",
        "opportunity_reference",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      await queryInterface.addColumn(
        "shipment_jobs",
        "move_coordinator",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      await queryInterface.addColumn(
        "shipment_jobs",
        "source",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      await queryInterface.addColumn(
        "shipment_jobs",
        "wo_reference",
        {
          type: Sequelize.STRING,
          allowNull:true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_jobs", "contact_reference");
      await queryInterface.removeColumn("shipment_jobs", "account_reference");
      await queryInterface.removeColumn("shipment_jobs", "opportunity_reference");
      await queryInterface.removeColumn("shipment_jobs", "move_coordinator");
      await queryInterface.removeColumn("shipment_jobs", "source");
      await queryInterface.removeColumn("shipment_jobs", "wo_reference");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("accesstokens", {
      accesstoken_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      admin_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "admins",
          key: "admin_id",
        }
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "companies",
          key: "company_id",
        }
      },
      staff_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        }
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "customers",
          key: "customer_id",
        }
      },
      access_token: {
        type: Sequelize.STRING,
        allowNull: false
      },
      device_token: {
        type: Sequelize.STRING,
        allowNull: true
      },
      device_type: {
        type: Sequelize.ENUM('ios', 'android', 'web'),
        defaultValue: "web",
      },
      version: {
        type: Sequelize.STRING(10),
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("accesstokens");
  },
};

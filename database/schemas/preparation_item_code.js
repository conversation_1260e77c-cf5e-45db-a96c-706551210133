"use strict";
module.exports = (sequelize, DataTypes) => {
	const preparation_item_code = sequelize.define(
		"preparation_item_code",
		{
			id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			code: DataTypes.STRING,
			created_at: {
				type: DataTypes.DATE,
			},
			updated_at: {
				type: DataTypes.DATE,
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	preparation_item_code.associate = function (models) {
	};
	return preparation_item_code;
};

"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_type_stage_for_shipment = sequelize.define(
		"shipment_type_stage_for_shipment",
		{
			local_shipment_stage_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			local_shipment_type_id: DataTypes.INTEGER,
			name: DataTypes.STRING,
			order_of_stages: DataTypes.INTEGER,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			scan_require: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			is_add_item: {
				type: DataTypes.TINYINT,
			},
			scan_into_storage: {
				type: DataTypes.TINYINT,
			},
			is_add_exceptions: {
				type: DataTypes.TINYINT,
			},
			supervisor_signature_require: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			customer_signature_require: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},

			why_supervisor_signature_require_note: DataTypes.STRING,
			why_customer_signature_require_note: DataTypes.STRING,
			supervisor_signature_require_note_by_user: DataTypes.STRING,
			customer_signature_require_note_by_user: DataTypes.STRING,

			supervisor_signature_require_at_origin_to_all_pages: {
				type: DataTypes.TINYINT,
			},

			supervisor_signature_require_at_destination_to_all_pages: {
				type: DataTypes.TINYINT,
			},

			customer_signature_require_at_origin_to_all_pages: {
				type: DataTypes.TINYINT,
			},

			customer_signature_require_at_destination_to_all_pages: {
				type: DataTypes.TINYINT,
			},

			ref_shipment_stage_id:
			{
				type: DataTypes.INTEGER,
			},
			shipment_job_id:
			{
				type: DataTypes.INTEGER,
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			scan_out_of_storage: {
				type: DataTypes.TINYINT,
			},
			PDF_time_require: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			show_no_exceptions: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 1
			},
			allow_default_manual_label: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			allow_default_qr_code_mode: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			add_items_to_inventory: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			assign_storage_units_to_items: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			unassign_storage_units_from_items: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			remove_items_to_inventory: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			enable_partial_complete_stage: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			total_add_items_inventory_stage: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			total_add_items_storage_stage: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			total_remove_items_storage_stage: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			total_remove_items_inventory_stage: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			total_add_items_to_inventory_scan: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			total_remove_items_to_inventory_scan: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			old_scan_require: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			remove_scan_require: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			}
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_type_stage_for_shipment.associate = function (models) {
		// associations can be defined here
		shipment_type_stage_for_shipment.belongsTo(models.shipment_type_for_shipment, {
			foreignKey: "local_shipment_type_id",
		});

		shipment_type_stage_for_shipment.belongsTo(models.shipment_job_forced, {
			foreignKey: "local_shipment_stage_id",
			targetKey: "local_altered_stage_id",
		});

		shipment_type_stage_for_shipment.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});

	};
	return shipment_type_stage_for_shipment;
};

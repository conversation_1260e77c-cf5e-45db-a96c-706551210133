const { StaticPages, contact_us, feedback } = require("../../database/schemas");

exports.getPageData = async (page_id) => {
  return await StaticPages.findOne({
    where: {
      page_id: page_id,
    },
    attributes: ['page_content'],
    raw: true,
  });
};

exports.contactUs = async (request) => {
  let insertData = {
    name: request.name,
    email: request.email,
    message: request.message
  };
  const contactUsData = await contact_us.create(insertData);

  return contactUsData;
};

exports.feedback = async (request) => {
  let insertData = {
    experience: request.experience,
    feedback: request.feedback
  };
  const feedbackData = await feedback.create(insertData);

  return feedbackData;
};

"use strict";
let bcrypt = require("bcrypt");

module.exports = (sequelize, DataTypes) => {
	const staff = sequelize.define(
		"staff",
		{
			staff_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			storage_staff_id: DataTypes.STRING(150),
			company_id: DataTypes.INTEGER,
			group_id: DataTypes.INTEGER,
			warehouse_id: DataTypes.STRING(150),
			first_name: DataTypes.STRING(50),
			notificationStatus: DataTypes.TINYINT,
			last_name: DataTypes.STRING(50),
			photo: DataTypes.TEXT,
			notes: DataTypes.TEXT,
			phone: DataTypes.STRING(15),
			country_code: DataTypes.STRING(10),
			email: DataTypes.STRING,
			password: DataTypes.STRING,
			push_notification: DataTypes.TINYINT,
			is_verified: DataTypes.TINYINT,
			verification_code: DataTypes.STRING,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			roles: {
				type: DataTypes.ENUM("ADMIN", "WORKE<PERSON>", "SUPERADMIN", "MAINADMIN"),
				defaultValue: "WORKER",
			},
			is_deleted: { type: DataTypes.TINYINT, defaultValue: 0 },

			is_admin_can_edit_shipment_type: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},

			is_allow_view_item: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 1
			},

			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			indexes: [
				{
					unique: true,
					fields: ["email"],
				},
			],
			hooks: {
				beforeCreate: (staff) => {
					if (
						staff.password &&
						staff.password !== "" &&
						staff.password !== "undefined"
					) {
						const salt = bcrypt.genSaltSync(10);
						staff.password = bcrypt.hashSync(staff.password, salt);
					}
				},
			},
		}
	);
	staff.associate = function (models) {
		// associations can be defined here
		staff.belongsTo(models.company, {
			as: "staff_company",
			foreignKey: "company_id",
		});

		staff.hasMany(models.shipment_job_assign_worker, {
			foreignKey: "staff_id",
		});

		staff.hasMany(models.shipment_job_signature, {
			as: "staff_signature",
			foreignKey: "staff_id",
		});

		staff.hasMany(models.shipment_inventory, {
			foreignKey: "carrier_packed_user_id",
		});

		staff.hasMany(models.shipment_inventory, {
			foreignKey: "disassembled_user_id",
		});

		staff.hasMany(models.shipment_inventory, {
			foreignKey: "prepared_by",
		});

		staff.hasMany(models.warehouse_list_staff, {
			as: "staff_warehouses",
			foreignKey: "staff_id",
		});
	};

	return staff;
};

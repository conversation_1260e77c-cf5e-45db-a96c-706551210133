'use strict';
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('companies', {
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
      },
      company_name: {
        type: Sequelize.STRING(150)
      },
      photo: {
        type: Sequelize.TEXT, 
        defaultValue: ''
      },
      address: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      phone: {
        type: Sequelize.STRING(15),
        defaultValue: ''
      },
      company_identity: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      email: {
        type: Sequelize.STRING
      },
      password: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      start_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      end_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      push_notification: {
        type: Sequelize.TINYINT,
        defaultValue: 1
      },
      is_verified: {
        type: Sequelize.TINYINT,
        defaultValue: 0
      },
      verification_code: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      status: {
        type: Sequelize.ENUM('inactive', 'active'), 
        defaultValue: 'active'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      }
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('companies');
  }
};
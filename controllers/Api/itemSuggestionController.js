const itemSuggestionModelApi = require("../../models/Api/itemSuggestionModel");
const commonFunction = require("../../assets/common");



exports.createItemSuggestionController = async (request, response) => {
    try {
        const itemSuggestionDetails = await itemSuggestionModelApi.createItemSuggestionModel(request.body)
        if (itemSuggestionDetails)
            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                1,
                ITEM_SUG_ADDED_SUCCESS,
                itemSuggestionDetails
            );
        else
            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                0,
                ITEM_SUG_ADDED_FAILURE,
                {}
            );
    }
    catch (reason) {
        console.log("exports.createItemSuggestionController -> error: ", reason);

        commonFunction.generateResponse(
            response,
            SERVER_ERROR_CODE,
            0,
            reason,
            {}
        )
    }
};


exports.getItemSuggestionListingController = async (request, response) => {
    try {
        request.query.search = request.query.search ? request.query.search : "";
        const itemSuggestionListing = await itemSuggestionModelApi.getItemListingModel(request.query, request.body)
        if (itemSuggestionListing)
            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                1,
                ITEM_SUG_RETRIEVED_SUCCESS,
                itemSuggestionListing
            );
        else
            commonFunction.generateResponse(
                response,
                SUCCESS_CODE,
                0,
                ITEM_SUG_NOT_FOUND,
                {}
            );
    }
    catch (reason) {
        console.log("exports.getItemSuggestionListingController -> error: ", reason);

        commonFunction.generateResponse(
            response,
            SERVER_ERROR_CODE,
            0,
            reason,
            {}
        )
    }
};
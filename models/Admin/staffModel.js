const {
	staff,
	company,
	sequelize,
	shipment_job_assign_worker,
	shipment_job_assign_worker_list,
	warehouse_list_staff,
	shipment_job,
	accesstoken,

} = require("../../database/schemas");
const bcrypt = require("bcrypt");
const { Op, literal, fn, where, col } = require("sequelize");
const shipmentModel = require("../Admin/shipmentModel");
const shipmentTypeModel = require("../../models/Admin/shipmentTypeModel");
const customerModel = require("../../models/Admin/customerModel");
const StaffModel = require("../../models/Admin/staffModel");
const roomModel = require("../../models/Admin/roomModel");
const itemSuggestionModel = require("../../models/Admin/itemSuggestionModel");
const tagModel = require("../../models/Admin/tagModel");

exports.staffListByCompanyModel = async (company_id) =>
	await staff.findAndCountAll({
		where: {
			is_deleted: 0,
			status: "active",
			roles: "WORKER",
			company_id: company_id,
		},
		attributes: [
			"staff_id",
			"roles",
			[sequelize.literal('CONCAT(first_name," ",last_name)'), "full_name"],
			"email",
		],
		order: [["first_name", "ASC"]],
		raw: true,
	});


exports.removeStaffModel = async (assignId) => await shipment_job_assign_worker_list.destroy({ where: { assign_job_worker_id: assignId } });

exports.addwarehousesToStaffModel = async (warehousesStaff, staffId) => {
	await warehouse_list_staff.destroy({ where: { staff_id: staffId } });
	await warehouse_list_staff.bulkCreate(warehousesStaff);
};

exports.addStaffWarehouse = async (body, staff) => {
	return await warehouse_list_staff.create({
		staff_id: staff.staff_id,
		warehouse_id: body.warehouseId
	})
}

exports.addStaffWarehouseById = async (body) => {
	return await warehouse_list_staff.create({
		staff_id: body.staff_id,
		warehouse_id: body.warehouseId
	})
}

exports.getStaffForWarehouse = async (body) => {
	return await staff.findOne({
		where: {
			storage_staff_id: body.staff_id
		}
	})
}

exports.fetchStaffForStorage = async (body) => {
	return staff.findOne({
		where: {
			staff_id: body.staff_id
		},
		attributes: [
			"staff_id",
			"storage_staff_id",
			"status",
			"email",
			[
				sequelize.literal(
					"(select integration_key FROM `company_integration_keys` where company_id = staff.company_id) "
				),
				"integration_key",
			],
		]
	})
}



exports.resetPassword = async (fields) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(fields.new_password, salt);
	let updatePwd = await staff.update(
		{ password: pwd, verification_code: null },
		{ where: { verification_code: fields.code } }
	);
	return updatePwd;
};

exports.verifyToken = async (code) => {
	let isVerify = await staff.count({
		where: {
			verification_code: code,
		},
	});
	return isVerify;
};

exports.staffStorageIdUpdate = async (request) => {
	try {
		return staff.update(
			{
				storage_staff_id: request.staffIdStorage,
			},
			{ where: { staff_id: request.staff_id } }
		);
	}
	catch (err) {
		console.log("err", err);
	}
}

exports.staffStorageIdUpdateByEmailFind = async (request) => {
	return staff.findOne({
		where: { email: request.staff_email },
		attributes: ["email", "storage_staff_id", "staff_id"]
	})
}

exports.staffStorageIdUpdateByEmail = async (request) => {
	try {
		return staff.update(
			{
				storage_staff_id: request.staffIdStorage,
				warehouse_id: request.warehouseId
			},
			{ where: { email: request.staff_email } }
		);
	}
	catch (err) {
		console.log("err", err);
	}
}

exports.addStaffStorage = async (request) => {
	let staffObj = {
		company_id: request.company_id && request.company_id,
		storage_staff_id: request.storage_staff_id && request.storage_staff_id,
		warehouse_id: request.warehouse_id && request.warehouse_id,
		first_name: request.first_name && request.first_name,
		last_name: request.last_name && request.last_name,
		notes: request.notes && request.notes,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		email: request.email && request.email,
		password: request.password && request.password,
		roles: request.roles && request.roles,
	};


	let staffDetails = await staff.create(staffObj);

	if (
		staffDetails.roles == 'ADMIN'
	) {
		let getShipmentsList = await shipmentModel.getShipmentJobListByCompanyId(staffDetails.company_id);
		for (let i = 0; i < getShipmentsList.length; i++) {
			let shipmentJobAssignToNewAdminStaff = await shipmentModel.shipmentJobAssignToNewAdminStaff(getShipmentsList[i].shipment_job_id, staffDetails.staff_id);
		}
	}
	return staffDetails;
};


exports.createSuperAdminStaffModel = async (request) => {
	let companyObj = {
		company_name: request.company_name && request.company_name,
		address1: request.address1 && request.address1,
		address2: request.address2 && request.address2,
		city: request.city && request.city,
		state: request.state && request.state,
		zipCode: request.zipCode && request.zipCode,
		country: request.country && request.country,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		company_identity: request.company_identity && request.company_identity,
		email: request.email && request.email,
		password: request.password && request.password,
		group_id: request.group_id && request.group_id,
		notes: request.notes && request.notes,
		roles: "COMPANYSUPERADMIN",
	};


	let companyDetails = await company.create(companyObj);

	let userLoginObj = {
		company_id: companyDetails.company_id,
		first_name: companyDetails.company_name,
		last_name: "Company User",
		email: companyDetails.email,
		group_id: request.group_id && request.group_id,
		password: request.password && request.password,
		roles: "SUPERADMIN",
	}

	let createUserForCompnay = await staff.create(userLoginObj)

	if (companyDetails && createUserForCompnay) {


		let fetchShipmentTypeForCompnay = await shipmentTypeModel.fetchShipmentTypeForCompnay(request);
		let createShipmentTypeForCompnay = await shipmentTypeModel.createShipmentTypeForCompnay(fetchShipmentTypeForCompnay, companyDetails);


		let newArrayData = [];
		for (let i = 0; i < createShipmentTypeForCompnay.length; i++) {
			let container = {};
			container.shipmentId = createShipmentTypeForCompnay[i].shipment_type_id;
			newArrayData.push(container)
		}

		for (let i = 0; i < fetchShipmentTypeForCompnay.length; i++) {
			let fetchShipmentTypeStagesForCompnay = await shipmentTypeModel.fetchShipmentTypeStagesForCompnay(fetchShipmentTypeForCompnay[i].shipment_type_id,);
			let createShipmentTypeStagesForCompnay = await shipmentTypeModel.createShipmentTypeStagesForCompnay(fetchShipmentTypeStagesForCompnay, newArrayData[i].shipmentId);
		}

		let fetchRoomListForCompnay = await roomModel.fetchRoomListForCompnay(request);
		let createRoomListForCompnay = await roomModel.createRoomListForCompnay(fetchRoomListForCompnay, companyDetails);

		let fetchItemListForCompnay = await itemSuggestionModel.fetchItemListForCompnay(request);
		let createItemListForCompnay = await itemSuggestionModel.createItemListForCompnay(fetchItemListForCompnay, companyDetails);




		let fetchTagListForCompnay = await tagModel.fetchTagListForCompnay(request);

		let createTagListForCompnay = await tagModel.createTagListForCompnay(fetchTagListForCompnay, companyDetails);

	}
	return companyDetails;
};

exports.addStaff = async (request, file) => {
	let staffObj = {
		company_id: request.company_id && request.company_id,
		warehouse_id: request.warehouse_id && request.warehouse_id,
		first_name: request.first_name && request.first_name,
		last_name: request.last_name && request.last_name,
		photo: file !== "" ? file : null,
		notes: request.notes && request.notes,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		email: request.email && request.email,
		password: request.password !== "" ? request.password : "123456",
		roles: request.roles === "ADMIN" ? "ADMIN" : "WORKER",
		is_admin_can_edit_shipment_type: request.is_admin_can_edit_shipment_type && request.is_admin_can_edit_shipment_type == "true" ? 1 : 0,
		is_allow_view_item: request.is_allow_view_item && request.is_allow_view_item == "true" ? 1 : 0
	};


	let staffDetails = await staff.create(staffObj);

	if (
		staffDetails.roles == 'ADMIN'
	) {
		let getShipmentsList = await shipmentModel.getShipmentJobListByCompanyId(staffDetails.company_id);
		for (let i = 0; i < getShipmentsList.length; i++) {
			let shipmentJobAssignToNewAdminStaff = await shipmentModel.shipmentJobAssignToNewAdminStaff(getShipmentsList[i].shipment_job_id, staffDetails.staff_id);
		}
	}
	return staffDetails;
};

exports.checkExistingEmail = async (request) => {
	return await staff.count({
		where: {
			email: request.email,
			is_deleted: 0
		},
	});
};

exports.getStaffListByCompanyId = async (companyId) => {
	return await staff.findAll({
		where: {
			company_id: companyId,
			roles: "ADMIN"
		}
	})
}

exports.shipmentJobAssignToOldAdminStaff = async (staffId, jobId) => {
	const staffDetails = {
		shipment_job_id: jobId,
		staff_id: staffId,
		role: "admin",
	}
	return await shipment_job_assign_worker.create(staffDetails);
}

exports.getStaffDetailsForCompnay = async (staffId) => {
	return await staff.findOne({
		where: {
			staff_id: staffId,
		},
	});
}

exports.getStaffList = async (company_id, request) => {
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	const status = request.filter ? request.filter : "active";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

	let matchObj = {};
	if (company_id) {
		matchObj = {
			[Op.and]: [
				{ company_id: company_id },
				{
					[Op.or]: [
						{ first_name: { [Op.like]: "%" + search + "%" } },
						{ last_name: { [Op.like]: "%" + search + "%" } },
						{ email: { [Op.like]: "%" + search + "%" } },
						{
							"$staff_company.company_name$": { [Op.like]: "%" + search + "%" },
						},
					],
					is_deleted: "0",
					status: status,
					roles: { [Op.not]: "SUPERADMIN" }
				},
			],
		};
	} else {
		matchObj = {
			[Op.or]: [
				{ first_name: { [Op.like]: "%" + search + "%" } },
				{ last_name: { [Op.like]: "%" + search + "%" } },
				{ email: { [Op.like]: "%" + search + "%" } },
				{ "$staff_company.company_name$": { [Op.like]: "%" + search + "%" } },
			],
			is_deleted: "0",
			status: status,
			roles: { [Op.not]: "SUPERADMIN" }
		};
	}
	return await staff.findAndCountAll({
		limit: pageSize,
		offset: start,
		where: matchObj,
		order:
			request.orderBy === "company_name"
				? [["staff_company", orderBy, orderSequence]]
				: [[orderBy, orderSequence]],
		attributes: [
			...COMMON_STAFF_ATTRIBUTES,
			[
				sequelize.literal(
					"(SELECT IF((select count(roles) from staffs where company_id = staff_company.company_id AND roles = 'ADMIN' AND is_deleted = 0) = 1, 'true', 'false'))"
				),
				"isSingleAdmin",
			],
			[
				sequelize.literal(
					`(CASE WHEN staff.photo IS NULL THEN '' ELSE staff.photo END)`
				),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN staff.photo IS NULL THEN '' WHEN staff.photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Staff_Profile}', 'original/', staff.photo) ELSE staff.photo END)`
				),
				"staff_profile",
			],
		],
		include: [
			{
				model: company,
				required: true,
				attributes: ["company_name"],
				as: "staff_company",
			},
		],
		raw: false,
	});
};

exports.getlistAllUsers = async () => {
	return await staff.findAll({
		where: {
			is_deleted: "0",
			status: "active",
			roles: { [Op.not]: "SUPERADMIN" }
		}, include: [
			{
				model: company,
				required: true,
				attributes: ["company_name"],
				as: "staff_company",
			},
		],
	});
}

exports.viewStaff = async (company_id, staff_id) => {
	let matchObj = {};
	if (company_id) {
		matchObj = {
			company_id: company_id,
			staff_id: staff_id,
		};
	} else {
		matchObj = { staff_id: staff_id };
	}
	return await staff.findOne({
		attributes: [
			...COMMON_STAFF_ATTRIBUTES,
			[
				sequelize.literal(`(CASE WHEN photo IS NULL THEN '' ELSE photo END)`),
				"photo",
			],
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' WHEN photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Staff_Profile}', 'original/', photo) ELSE photo END)`
				),
				"staff_profile",
			],
		],
		include: [
			{
				model: warehouse_list_staff,
				as: "staff_warehouses",
				required: false,
				attributes: ["staff_id", "warehouse_id"],
			},
		],
		where: matchObj
	});
};

exports.checkReExistingEmail = async (request) => {
	return await staff.count({
		where: {
			email: request.email,
			staff_id: { [Op.ne]: request.staff_id },
			company_id: request.company_id,
			is_deleted: 0
		},
	});
};

exports.checkReExistingEmailForStorage = async (request) => {
	return await staff.count({
		where: {
			email: request.email,
			storage_staff_id: { [Op.ne]: request.staff_id },
			company_id: request.company_id,
			is_deleted: 0
		},
	});
};

exports.editStaff = async (request, file) => {
	let staffObj = {
		company_id: request.company_id && request.company_id,
		first_name: request.first_name && request.first_name,
		warehouse_id: request.warehouse_id && request.warehouse_id,
		last_name: request.last_name && request.last_name,
		notes: request.notes && request.notes,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		email: request.email && request.email,
		roles: request.roles === "ADMIN" ? "ADMIN" : "WORKER",
		password: request.password !== "" ? request.password : "123456",
		updated_at: new Date(),
		is_admin_can_edit_shipment_type: request.is_admin_can_edit_shipment_type && request.is_admin_can_edit_shipment_type == "true" ? 1 : 0,
		is_allow_view_item: request.is_allow_view_item && request.is_allow_view_item == "true" ? 1 : 0
	};

	staffObj["photo"] = file !== "" && file !== null && file !== undefined ? file : null;


	return await staff.update(
		staffObj,
		{ where: { staff_id: request.staff_id } }
	);
};


exports.editStaffStorage = async (request, file) => {
	let staffObj = {
		company_id: request.company_id && request.company_id,
		first_name: request.first_name && request.first_name,
		last_name: request.last_name && request.last_name,
		notes: request.notes && request.notes,
		phone: request.phone && request.phone,
		country_code: request.country_code && request.country_code,
		email: request.email && request.email,
		roles: request.roles === "ADMIN" ? "ADMIN" : "WORKER",
		password: request.password !== "" ? request.password : "123456",
		updated_at: new Date(),
	};

	staffObj["photo"] = file !== "" && file !== null && file !== undefined ? file : null;


	return await staff.update(
		staffObj,
		{ where: { storage_staff_id: request.staff_id } }
	);
};


exports.changeStaffStatus = async (request) => {
	return await staff.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { staff_id: request.staff_id } }
	);
};

exports.changeStaffStatusStorage = async (request) => {
	if (request.isDeleted) {
		return await staff.update(
			{
				is_deleted: 1,
			},
			{ where: { storage_staff_id: request.staff_id } }
		);
	}
	else {
		return await staff.update(
			{
				status: request.isActive == 1 ? "active" : "inactive"
			},
			{ where: { storage_staff_id: request.staff_id } }
		);
	}


};

exports.deleteStaff = async (request) => {
	return await staff.update(
		{
			is_deleted: literal('CASE WHEN is_deleted = "0" THEN "1" ELSE "0" END'),
		},
		{ where: { staff_id: request.staff_id } }
	);
};

exports.deleteStaffShipmentDelete = async (request) => {
	await shipment_job_assign_worker.destroy({ where: { staff_id: request.staff_id } });
}

exports.deleteAccessToken = async (request) => {
	await accesstoken.destroy({ where: { staff_id: request.staff_id } });
};

exports.viewCompanyList = async (request) => {
	return await company.findAll({
		attributes: ["company_id", "company_name"],
		where: {
			is_deleted: "0",
			status: "active",
			roles: "COMPANYADMIN"
		},
		raw: true,
	});
};

exports.isStaffModel = async (staffId) => {
	const isStaff = await staff
		.findOne({
			where: { staff_id: staffId, status: "active", is_deleted: "0" }, // is_verified: 1,
			attributes: ["staff_id"],
			raw: true,
		})
	if (isStaff !== null) {
		return true
	}
	else {
		return false
	}
}

exports.getStaffBasicData = async (staffType, filter) =>
	await staff.findAll({
		where: {
			is_deleted: 0,
			status: "active",
			[Op.or]: [
				where(fn("concat", col("first_name"), " ", col("last_name")), {
					[Op.like]: "%" + filter.search + "%",
				}),
			],
		},
		attributes: [
			"staff_id",
			[sequelize.literal('CONCAT(first_name," ",last_name)'), "full_name"],
		],
		order: [[fn("concat", col("first_name"), " ", col("last_name"))]],
		raw: true,
	});


exports.getStaffListWithJobAssignedCompanyData = async (shipmentId, company_id) =>
	await staff.findAndCountAll({
		where: {
			is_deleted: 0,
			status: "active",
			roles: "WORKER",
			company_id: company_id,
		},
		attributes: [
			"staff_id",
			"roles",
			[sequelize.literal('CONCAT(first_name," ",last_name)'), "full_name"],
			"email",
		],
		distinct: "staff_id",
		include: [
			{
				model: shipment_job_assign_worker,
				as: "shipment_job_assign_workers",
				where: { shipment_job_id: shipmentId },
				required: false,
				attributes: [
					"role",
					"shipment_job_worker_id",
					[
						sequelize.literal(
							`CASE WHEN shipment_job_assign_workers.shipment_job_id = ${shipmentId} THEN true ELSE false END`
						),
						"is_assigned",
					],
				],

				include: [
					{
						model: shipment_job,
						attributes: ["shipment_name", "job_number"],
						required: false,
					},
				],
			},
			{
				model: company,
				required: true,
				attributes: ["company_name"],
				as: "staff_company",
			},
		],
		order: [["first_name", "ASC"]],
		raw: true,
	});


exports.getStaffListWithJobAssignedData = async (shipmentId, company_id) =>
	await shipment_job_assign_worker_list.findAndCountAll({
		where: {
			shipment_job_id: shipmentId,
		},
		attributes: [
			"staff_id",
			"role",
			"assign_job_worker_id",
			"local_shipment_stage_id",
			[
				sequelize.literal(
					"(select email FROM `staffs` where staff_id = shipment_job_assign_worker_list.staff_id)"
				),
				"email",
			],
			[
				sequelize.literal(
					"(SELECT CONCAT(first_name, ' ', last_name) FROM `staffs` WHERE staff_id = shipment_job_assign_worker_list.staff_id)"
				),
				"full_name",
			]
		],
		order: [["created_at", "ASC"]],
	});


exports.updatePassword = async (request) => {
	const salt = bcrypt.genSaltSync(10);
	let staffobj = {
		password: bcrypt.hashSync(request.password, salt),
	};
	return await staff.update(staffobj, {
		where: {
			staff_id: request.staff_id,
		},
	});
};

exports.updatePasswordCompany = async (request, email) => {
	const salt = bcrypt.genSaltSync(10);
	let obj = {
		password: bcrypt.hashSync(request.password, salt),
	};
	return await company.update(obj, {
		where: {
			email: email,
		},
	});
};


exports.findCompanyByEmail = (email) =>
	company.findOne({
		where: {
			email: email,
			is_deleted: "0"
		},
	});

exports.checkStaffAssignToJob = async (id) => {
	return await shipment_job_assign_worker.findAndCountAll({
		where: {
			staff_id: id,
		},
	});
};

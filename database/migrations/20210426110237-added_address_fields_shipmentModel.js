"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn(
				"shipment_jobs",
				"pickup_city",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"pickup_state",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"pickup_zipcode",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"pickup_country",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"delivery_city",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"delivery_state",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"delivery_zipcode",
				Sequelize.STRING
			);
			await queryInterface.addColumn(
				"shipment_jobs",
				"delivery_country",
				Sequelize.STRING
			);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("shipment_jobs", "pickup_city");
			await queryInterface.removeColumn("shipment_jobs", "pickup_state");
			await queryInterface.removeColumn("shipment_jobs", "pickup_zipcode");
			await queryInterface.removeColumn("shipment_jobs", "pickup_country");
			await queryInterface.removeColumn("shipment_jobs", "delivery_city");
			await queryInterface.removeColumn("shipment_jobs", "delivery_state");
			await queryInterface.removeColumn("shipment_jobs", "delivery_zipcode");
			await queryInterface.removeColumn("shipment_jobs", "delivery_country");
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};

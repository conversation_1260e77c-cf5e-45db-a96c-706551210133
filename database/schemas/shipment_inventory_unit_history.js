"use strict";
module.exports = (sequelize, DataTypes) => {
  const shipment_inventory_unit_history = sequelize.define(
    "shipment_inventory_unit_history",
    {
      shipment_inventory_unit_history_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      shipment_inventory_id: DataTypes.INTEGER,
      shipment_job_id: DataTypes.INTEGER,
      storage_unit_id: DataTypes.STRING(150),
      unit_id: DataTypes.INTEGER,
			add_stage_id: DataTypes.INTEGER,
			remove_stage_id: DataTypes.INTEGER,
      qr_id: DataTypes.INTEGER,
      item_name: DataTypes.STRING,
      item_weight: DataTypes.DECIMAL(11, 2),
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE
      }
    },
    { createdAt: false, updatedAt: false, paranoid: true }
  );
  shipment_inventory_unit_history.associate = function (models) {
    shipment_inventory_unit_history.belongsTo(models.shipment_job, {
      foreignKey: "shipment_job_id",
    });
    shipment_inventory_unit_history.belongsTo(models.shipment_inventory, {
      as: "shipment_inventory",
      foreignKey: "shipment_inventory_id",
    });
  };
  return shipment_inventory_unit_history;
};

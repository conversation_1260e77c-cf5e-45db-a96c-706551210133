'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.addColumn(
                'groups',
                'pdf_time_stamp_checked',
                {
                    type: Sequelize.TINYINT,
                    allowNull: true,
                    defaultValue: 0,
                },
                {
                    logging: true,
                }
            );
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },

    down: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.removeColumn('groups', 'pdf_time_stamp_checked');
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },
};

"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn(
        'shipment_inventory_photos',
        'stage_id',
        {
          type: Sequelize.INTEGER(11),
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "shipment_type_stages",
            key: "shipment_stage_id",
          },
        }
    );
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('shipment_inventory_photos', 'stage_id');
  },
};

const axios = require('axios');

async function testBulkCreateRoomIsoCodes() {
    try {
        console.log("🚀 Testing Bulk Create Room ISO Codes from Excel API");
        console.log("====================================================");
        
        // API endpoint
        const apiUrl = 'http://localhost:8421/api/admin/room/bulk-create-iso-codes-from-excel';
        
        console.log("📡 Making API call to:", apiUrl);
        console.log("📋 Expected behavior:");
        console.log("   1. Read shipment_rooms.xlsx file from docs folder");
        console.log("   2. Extract data and map to room_iso_code table structure:");
        console.log("      - code: iso_full_id from Excel");
        console.log("      - name: name from Excel");
        console.log("      - annex: annex from Excel");
        console.log("   3. Bulk create records in room_iso_code table");
        console.log("\n⏳ Processing...\n");
        
        // Test data - you can modify these options
        const requestData = {
            clear_existing: false // Set to true to clear existing data before import
        };
        
        // Make API call
        const response = await axios.post(apiUrl, requestData, {
            timeout: 60000 // 60 seconds timeout
        });
        
        console.log("✅ API Response Status:", response.status);
        
        if (response.data.status === 1) {
            console.log("🎉 API call successful!");
            console.log("📊 BULK CREATE RESULTS:");
            console.log("====================================================");
            
            const data = response.data.data;
            
            console.log(`📋 Total Excel records processed: ${data.total_excel_records}`);
            console.log(`💾 Total records created in database: ${data.total_created_records}`);
            
            // Show sample data that was processed
            console.log("\n🔍 Sample Data Processed:");
            if (data.sample_data && data.sample_data.length > 0) {
                data.sample_data.forEach((record, index) => {
                    console.log(`\n   Record ${index + 1}:`);
                    console.log(`     code: "${record.code}"`);
                    console.log(`     name: "${record.name}"`);
                    console.log(`     annex: "${record.annex}"`);
                });
            }
            
            // Show created records info
            console.log("\n💾 Created Records Info:");
            if (data.created_records && data.created_records.length > 0) {
                console.log(`   First created record ID: ${data.created_records[0].id}`);
                console.log(`   Last created record ID: ${data.created_records[data.created_records.length - 1].id}`);
                
                // Show first few created records
                console.log("\n   First 3 created records:");
                data.created_records.slice(0, 3).forEach((record, index) => {
                    console.log(`     ${index + 1}. ID: ${record.id}, Code: "${record.code}", Name: "${record.name}", Annex: "${record.annex}"`);
                });
            }
            
            console.log("\n✅ SUCCESS: Room ISO codes bulk created successfully!");
            
        } else {
            console.log("⚠️ API returned error:", response.data.message);
            console.log("📄 Full response:", JSON.stringify(response.data, null, 2));
        }
        
    } catch (error) {
        console.log("\n❌ ERROR OCCURRED:");
        console.log("===============================================");
        
        if (error.response) {
            console.log("📡 API Error Response:");
            console.log("Status:", error.response.status);
            console.log("Message:", error.response.data?.message || 'No message');
            console.log("Full Response:", JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log("🌐 Network Error - No response received");
            console.log("Make sure the server is running on port 8421");
            console.log("Command to start server: npm start or node server.js");
        } else {
            console.log("⚠️ Error:", error.message);
        }
    }
}

// Test with clearing existing data
async function testWithClearExisting() {
    try {
        console.log("\n" + "=".repeat(60));
        console.log("🧹 Testing with clear_existing = true");
        console.log("=".repeat(60));
        
        const apiUrl = 'http://localhost:8421/api/admin/room/bulk-create-iso-codes-from-excel';
        const requestData = {
            clear_existing: true // This will clear existing data before import
        };
        
        const response = await axios.post(apiUrl, requestData, {
            timeout: 60000
        });
        
        if (response.data.status === 1) {
            console.log("✅ Clear and create successful!");
            console.log(`📊 Records created: ${response.data.data.total_created_records}`);
        } else {
            console.log("⚠️ Error:", response.data.message);
        }
        
    } catch (error) {
        console.log("❌ Error in clear and create test:", error.message);
    }
}

// Instructions for testing
console.log("📋 TESTING INSTRUCTIONS:");
console.log("========================");
console.log("1. Make sure your server is running: npm start");
console.log("2. Ensure the Excel file exists in docs/shipment_rooms.xlsx");
console.log("3. Make sure the room_iso_code table exists in your database");
console.log("4. Expected Excel columns: iso_full_id, name, annex");
console.log("5. Authentication is disabled for testing");
console.log("6. Check server console for detailed processing logs\n");

// Run the test
console.log("🚀 Running basic test...");
testBulkCreateRoomIsoCodes().then(() => {
    // Uncomment the line below to also test with clearing existing data
    // return testWithClearExisting();
});

"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_jobs",
        "is_job_complete_flag",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_jobs", "is_job_complete_flag");
       return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

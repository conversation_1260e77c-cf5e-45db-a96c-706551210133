const express = require("express");
const router = express();
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const { errorHandler } = require("../assets/common");
const multer = require("multer");
const upload = multer({ dest: "temp/" });
const shipmentController = require("../controllers/Admin/shipmentController");

/**
* @desc webhook for fetch shipment deatils from mover storage .
* @method POST
*/

router
    .route("/shipment/:shipmentId")
    .post(
        upload.none(),
        validators.admin.webhookShipment,
        errorHandler,
        shipmentController.webhookShipmentDetails
    );

/**
* @desc webhook for fetch unit deatils from mover storage .
* @method POST
*/

router
    .route("/unit/:unitId")
    .post(
        upload.none(),
        validators.admin.webhookUnit,
        errorHandler,
        shipmentController.webhookUnitDetails
    );

module.exports = router;
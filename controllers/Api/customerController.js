const { sequelize } = require("../../database/schemas");
const customerModel = require("../../models/Admin/customerModel");
const customerModelApi = require("../../models/Api/customerModel");
const companyModel = require("../../models/Admin/companyModel");
const commonFunction = require("../../assets/common");
const AWS = require("aws-sdk");
const fs = require("fs");
const { sendEmail, readFile } = require("../../assets/common");
const axios = require("axios");


exports.consumerLoginJsonFun = async (request, response, integration_key) => {
    const consumerLoginJson = JSON.stringify({
        companyIdTokenMoverInventory: integration_key,
        email: "<EMAIL>",
        password: "5PLaRAqq",
        deviceToken: "abcd",
        deviceType: 0,
    });
    try {
        const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
            headers: {
                'Content-Type': 'application/json'
            }
        })
        if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
            return consumerLoginResponse.data;
        }
        else {
            commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
        }

    } catch (error) {
        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
    }
}

exports.addCustomerJson = async (request, response, CustomerData, consumerLoginJson, getCompanyData) => {
    const addCustomerJson = JSON.stringify({
        importCustomerId: CustomerData.customer_id,
        firstName: (CustomerData.first_name !== undefined) ? CustomerData.first_name : "",
        lastName: (CustomerData.last_name !== undefined) ? CustomerData.last_name : "",
        address: {
            addressLine1: (CustomerData.address1 !== undefined) ? CustomerData.address1 : "",
            addressLine2: (CustomerData.address2 !== undefined) ? CustomerData.address2 : "",
            city: (CustomerData.city !== undefined) ? CustomerData.city : "",
            state: (CustomerData.state !== undefined) ? CustomerData.state : "",
            zipcode: (CustomerData.zipCode !== undefined) ? CustomerData.zipCode : "",
            country: (CustomerData.country !== undefined) ? CustomerData.country : "",
        },
        email: [
            (CustomerData.email !== undefined) ? CustomerData.email : "",
            (CustomerData.email2 !== undefined) ? CustomerData.email2 : "",
        ],
        phoneNumber: [
            (CustomerData.phone !== undefined) ? CustomerData.phone : "",
            (CustomerData.phone2 !== undefined) ? CustomerData.phone2 : "",
            (CustomerData.phone3 !== undefined) ? CustomerData.phone3 : "",
        ],
        accountId: (CustomerData.account_id !== undefined) ? CustomerData.account_id : "",
        accountName: (CustomerData.account_name !== undefined) ? CustomerData.account_name : "",
        salesRep: (CustomerData.sales_rep !== undefined) ? CustomerData.sales_rep : "",
        companyId: getCompanyData.key_company.storage_company_id,
        importedTags: [

        ],
        moverInventoryCustomerId: CustomerData.customer_id,
        createdFromMoverInventory: true,
    })

    try {
        const addCustomerResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, addCustomerJson, {
            headers: {
                'Content-Type': 'application/json',
                'accessToken': consumerLoginJson.data.accessToken,
            }
        })
        if (addCustomerResponse) {
            let params = {
                customer_id: CustomerData.customer_id,
                customerIdStorage: addCustomerResponse.data.data.id
            };

            const customerDetail = await customerModel.customerStorageIdUpdate(params);
            return customerDetail
        }
        else {
            commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
        }

    } catch (error) {
        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Customer add Fail", {});
    }
}

/**Post /customer/add  addCustomer Controller */
exports.addCustomer = async (request, response) => {
    try {
        //here we check email not same as company email
        const emailCheckCompany = await companyModel.checkExistingEmail(
            request.body.email
        );
        if (emailCheckCompany > 0) {
            return response.status(CONFLICT_CODE).json({
                status: 0,
                message: "Email can't be same as company email, so please choose another!. ",
                data: {},
            })
        }
        //here we check email not exits
        const emailCount = await customerModelApi.checkReExistingEmailAdd(request.body);
        if (emailCount) {
            response.status(CONFLICT_CODE).json({
                status: 0,
                message: CUSTOMER_EMAIL_EXIST,
                data: {},
            });
        } else {
            let newFileName = "";
            let headerData = await commonFunction.jwtTokenDecode(request.headers.access_token);
            if (request.file && request.file.originalname !== "") {
                const fileName = request.file.filename;
                const fileExt = request.file.originalname.split(".");
                newFileName = fileName + "." + fileExt[1];
                const s3 = new AWS.S3();
                let params = {
                    ACL: "public-read",
                    Bucket: Const_AWS_BUCKET,
                    Body: fs.createReadStream(request.file.path),
                    Key: Const_AWS_Customer_Profile + "original/" + newFileName,
                };
                let result = await commonFunction.UploadImageS3(params, s3);
                if (result) {
                    fs.unlinkSync(request.file.path);
                }
            }
            try {
                const customerDetail = await customerModel.addCustomer(request.body, newFileName)
                const getCompanyData = await customerModelApi.getCompanyData(request.body)
                if (getCompanyData) {
                    const consumerLoginJson = await this.consumerLoginJsonFun(request, response, getCompanyData.integration_key);
                    const addCustomerJson = await this.addCustomerJson(request, response, customerDetail, consumerLoginJson, getCompanyData)
                }
                //newChanges
                if (request.body.tag) {
                    let tagCustomer = [];
                    request.body.tag.map((tag) =>
                        tagCustomer.push({
                            customer_id: customerDetail.customer_id,
                            tag_id: tag,
                        })
                    );
                    //here we create customer

                    await customerModel.addTagToCustomerModel(tagCustomer, customerDetail.customer_id);
                }
                response.status(SUCCESS_CODE).json({
                    status: 1,
                    message: CUSTOMER_ADD_SUCCESS,
                    data: customerDetail,
                });
            } catch (error) {
                response.status(SERVER_ERROR_CODE).json({
                    status: 0,
                    message: error.message,
                    data: {},
                });
            }
        }
    } catch (error) {
        console.log("exports.addCustomer -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};


/**Post /customer/list getCustomerList Controller */
exports.getCustomerList = async (request, response) => {
    try {
        const { company_id } = request.body;
        //here we fetch customer list
        const customerList = await customerModelApi.getCustomerList(company_id, request.query);
        if (customerList) {
            response.status(SUCCESS_CODE).json({
                status: 1,
                message: CUSTOMER_RETRIEVED_SUCCESS,
                data: customerList
            });
        }
        else {
            response.status(EXPECTATION_FAILED_CODE).json({
                status: 0,
                message: CUSTOMER_NOT_FOUND,
                data: {},
            });
        }
    } catch (error) {
        console.log("exports.getCustomerList -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};

/**Post /mail_customer mailShipmentDetailToCustomerController Controller */
exports.mailShipmentDetailToCustomerController = async (request, response, next) => {
    try {
        const { model_customer_id, model_customer_email } = request.body;
        if (!model_customer_id)
            return commonFunction.generateResponse(response, SUCCESS_CODE, 0, CUSTOMER_NOT_FOUND, {});

        let html = await commonFunction.readFile("customerPortal.html");

        //here we find customer details

        const customerDetails = await customerModel.findCustomer(model_customer_id)

        if (customerDetails.email) {
            html = html.replace(
                /{{customer_name}}/g,
                `${customerDetails.first_name ? customerDetails.first_name : ""} ${customerDetails.last_name ? customerDetails.last_name : ""
                }`
            );
            html = html.replace(
                /{{company_name}}/g,
                `${customerDetails && customerDetails.customer_company
                    ? customerDetails.customer_company.company_name
                    : ""
                }`
            );
            html = html.replace(
                /{{company_email}}/g,
                `${customerDetails && customerDetails.customer_company
                    ? customerDetails.customer_company.email
                    : ""
                }`
            );
            html = html.replace(
                /{{company_phone}}/g,
                `${customerDetails && customerDetails.customer_company
                    ? customerDetails.customer_company.phone
                    : ""
                }`
            );
            html = html.replace(/{{CUSTOMER_PORTAL_LINK}}/g, CUSTOMER_PORTAL_LINK);
            //newChanges
            const returnResponse = await sendEmail(model_customer_email, "Your customer portal link", html);
            if (returnResponse) {
                next()
            }

        } else {
            return commonFunction.generateResponse(response, SUCCESS_CODE, 0, MAIL_NOT_FOUND, {});
        }

    } catch (error) {
		console.log("exports.mailShipmentDetailToCustomerController -> error: ", error);

        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
    }
};


/**Post /customer/view-customer viewCustomer Controller */
exports.viewCustomer = async (request, response) => {
    try {
        const customerDetail = await customerModel.viewCustomer(
            request.company_id,
            request.body.customer_id
        );
        if (customerDetail !== "") {
            response.status(SUCCESS_CODE).json({
                message: CUSTOMER_RETRIEVED_SUCCESS,
                data: customerDetail,
            });
        } else {
            response.status(EXPECTATION_FAILED_CODE).json({
                message: CUSTOMER_NOT_FOUND,
                data: {},
            });
        }
    } catch (error) {
        console.log("exports.viewCustomer -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};


exports.editCustomer = async (request, response) => {
    try {

        const emailCount = await customerModel.checkReExistingEmail(request.body);
        if (emailCount > 0) {
            response.status(CONFLICT_CODE).json({
                status: 1,
                message: CUSTOMER_EMAIL_EXIST,
                data: {},
            });
        }
        else {
            const s3 = new AWS.S3();
            try {
                let headerData = await commonFunction.jwtTokenDecode(request.headers.access_token);
                if (request.body.retain_photo === "false") {
                    const oldCustomerData = await customerModel.viewCustomer(
                        request.company_id,
                        request.body.customer_id
                    );
                    if (oldCustomerData.photo) {
                        let delMedia = {
                            Bucket: Const_AWS_BUCKET,
                            Key: Const_AWS_Customer_Profile + "original/" + oldCustomerData.photo,
                        };
                        await commonFunction.deleteImageS3(delMedia, s3);
                    }
                }
                if (request.file && request.file.originalname !== "") {
                    let newFileName = "";
                    const oldCustomerData = await customerModel.viewCustomer(
                        request.company_id,
                        request.body.customer_id
                    );
                    const fileName = request.file.filename;
                    const fileExt = request.file.originalname.split(".");
                    newFileName = fileName + "." + fileExt[1];

                    let params = {
                        ACL: "public-read",
                        Bucket: Const_AWS_BUCKET,
                        Body: fs.createReadStream(request.file.path),
                        Key: Const_AWS_Customer_Profile + "original/" + newFileName,
                    };
                    if (oldCustomerData.photo) {
                        let delMedia = {
                            Bucket: Const_AWS_BUCKET,
                            Key: Const_AWS_Customer_Profile + "original/" + oldCustomerData.photo,
                        };
                        await commonFunction.deleteImageS3(delMedia, s3);
                    }

                    let result = await commonFunction.UploadImageS3(params, s3);

                    if (result) {
                        fs.unlinkSync(request.file.path);
                        request.body.photo = newFileName;
                    }
                }

                const customerCount = await customerModel.checkExistingCustomer(request.body);
                if (customerCount > 0) {
                    try {
                        const customerDetail = await customerModel.editCustomer(request.body);
                        //newChanges
                        if (request.body.tag) {
                            let tagCustomer = [];
                            request.body.tag.map((tag) =>
                                tagCustomer.push({
                                    customer_id: request.body.customer_id,
                                    tag_id: tag,
                                })
                            );
                            await customerModel.addTagToCustomerModel(tagCustomer, request.body.customer_id);
                        }
                        response.status(SUCCESS_CODE).json({
                            status: 1,
                            message: CUSTOMER_UPDATED_SUCCESS,
                            data: {},
                        });
                    } catch (error) {
                        response.status(EXPECTATION_FAILED_CODE).json({
                            status: 0,
                            message: CUSTOMER_UPDATE_FAIL,
                            data: {},
                        });
                    }
                } else {
                    response.status(SUCCESS_CODE).json({
                        status: 0,
                        message: CUSTOMER_NOT_FOUND,
                        data: {},
                    });
                }
            } catch (error) {
                response.status(EXPECTATION_FAILED_CODE).json({
                    status: 0,
                    message: CUSTOMER_UPDATE_FAIL,
                    data: {},
                });
            }
        }
    } catch (error) {
        console.log("exports.editCustomer -> error: ", error);
        response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: error.message,
            data: {},
        });
    }
};
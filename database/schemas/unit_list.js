"use strict";
module.exports = (sequelize, DataTypes) => {
  const unit_list = sequelize.define(
    "unit_list",
    {
      unit_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      storage_unit_id: DataTypes.STRING,
      name: DataTypes.STRING,
      status: {
				type: DataTypes.ENUM("empty", "occupied"),
				defaultValue: "empty",
			},
      unitCode: DataTypes.STRING,
      isActive: DataTypes.TINYINT,
      currentLocation: DataTypes.STRING,
      numericLocation: DataTypes.INTEGER,
      unitNotes: DataTypes.STRING,
      addedBy: DataTypes.STRING,
      number: DataTypes.INTEGER,
      warehouseId: DataTypes.STRING,
      customerId: DataTypes.STRING,
      shipmentId: DataTypes.STRING,
      roomId: DataTypes.STRING,
      unitTypeId: DataTypes.STRING,
      unitTypeName: DataTypes.STRING,
      shipment_job_id: DataTypes.INTEGER,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
    },
    { createdAt: false, updatedAt: false }
  );
  unit_list.associate = function (models) {

    unit_list.belongsTo(models.shipment_job, {
      foreignKey: "shipment_job_id",
    });

    unit_list.hasMany(models.shipment_inventory, {
      as: "unit_items",
      foreignKey: "unit_id",
    });

    unit_list.belongsTo(models.shipment_job, {
      foreignKey: "warehouseId",
    });


  };
  return unit_list;
};

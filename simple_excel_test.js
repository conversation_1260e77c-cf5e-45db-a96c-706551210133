const ExcelJS = require("exceljs");

async function readExcel() {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile("docs/shipment_rooms.xlsx");
    
    const worksheet = workbook.getWorksheet(1);
    console.log("Rows:", worksheet.rowCount);
    
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber <= 10) {
            console.log(`Row ${rowNumber}: ${row.getCell(1).value} | ${row.getCell(2).value}`);
        }
    });
}

readExcel().catch(console.error);

"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_inventory_job_scanned", {
      scan_status_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER(11),
      },
      shipment_inventory_id: {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_inventories",
          key: "shipment_inventory_id",
        },
      },
      current_stage_id: {
        type: Sequelize.INTEGER(11),
        allowNull: false,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_type_stages",
          key: "shipment_stage_id",
        },
      },
      shipment_job_id: {
        type: Sequelize.INTEGER(11),
        allowNull: false,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_jobs",
          key: "shipment_job_id",
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_inventory_job_scanned");
  },
};

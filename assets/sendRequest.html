<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">

  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="UTF-8" />
    <title>Forgot Password</title>
    <style type="text/css" rel="stylesheet" media="all">
      /* Base ------------------------------ */
      *:not(br):not(tr):not(html) {
        font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
      }

      body {
        width: 100% !important;
        height: 100%;
        margin: 0;
        line-height: 1.4;
        background-color: #f5f7f9;
        color: #839197;
      }

      a {
        color: #414ef9;
      }

      table tr td {
        font-size: 16px;
      }

      table tr td:nth-of-type(1) {
        color: #414ef9;
        font-weight: 600;
      }

      /* Layout ------------------------------ */
      .email-wrapper {
        width: 100%;
        margin: 0;
        padding: 0;
        background-color: #f5f7f9;
      }

      .email-content {
        width: 100%;
        margin: 0;
        padding: 0;
      }

      /* Masthead ----------------------- */
      .email-masthead {
        padding: 25px 0;
        text-align: center;
      }

      .email-masthead_logo {
        max-width: 400px;
        border: 0;
      }

      .email-masthead_name {
        font-size: 16px;
        font-weight: bold;
        color: #839197;
        text-decoration: none;
        text-shadow: 0 1px 0 white;
      }

      /* Body ------------------------------ */
      .email-body {
        width: 100%;
        margin: 0;
        padding: 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        background-color: #ffffff;
      }

      .email-body_inner {
        width: 570px;
        margin: 0 auto;
        padding: 0;
      }

      .email-footer {
        width: 570px;
        margin: 0 auto;
        padding: 0;
        text-align: center;
      }

      .email-footer p {
        color: #839197;
      }

      .body-action {
        width: 100%;
        margin: 30px auto;
        padding: 0;
        text-align: center;
      }

      .body-sub {
        margin-top: 25px;
        padding-top: 25px;
        border-top: 1px solid #e7eaec;
      }

      .content-cell {
        padding: 35px;
      }

      .align-right {
        text-align: right;
      }

      /* Type ------------------------------ */
      h1 {
        margin-top: 0;
        color: #292e31;
        font-size: 19px;
        font-weight: bold;
        text-align: left;
      }

      h2 {
        margin-top: 0;
        color: #292e31;
        font-size: 16px;
        font-weight: bold;
        text-align: left;
      }

      h3 {
        margin-top: 0;
        color: #292e31;
        font-size: 14px;
        font-weight: bold;
        text-align: left;
      }

      p {
        margin-top: 0;
        color: #839197;
        font-size: 16px;
        line-height: 1.5em;
        text-align: left;
      }

      p.sub {
        font-size: 12px;
      }

      p.center {
        text-align: center;
      }

      /* Buttons ------------------------------ */
      .button {
        display: inline-block;
        width: 200px;
        background-color: #414ef9;
        border-radius: 3px;
        color: #ffffff;
        font-size: 15px;
        line-height: 45px;
        text-align: center;
        text-decoration: none;
      }

      .button--green {
        background-color: #28db67;
      }

      .button--red {
        background-color: #ff3665;
      }

      .button--blue {
        background-color: #414ef9;
      }

      .button--black {
        background-color: #000000;
      }

      /*Media Queries ------------------------------ */
      @media only screen and (max-width: 600px) {

        .email-body_inner,
        .email-footer {
          width: 100% !important;
        }
      }

      @media only screen and (max-width: 500px) {
        .button {
          width: 100% !important;
        }
      }
    </style>
  </head>

  <body>
    <h2 style="margin-top: 25px;">New application request!</h2>
    <div class="email-wrapper" style="width: 100%;margin-top: 25px;">
      <table cellpadding="5" cellspacing="5">
        <tbody>
          <tr>
            <td>Name</td>
            <td>:- </td>
            <td>{{full_name}}</td>
          </tr>
          <tr>
            <td>Company</td>
            <td>:- </td>
            <td>{{company_name}}</td>
          </tr>
          <tr>
            <td>Email</td>
            <td>:- </td>
            <td>{{email}}</td>
          </tr>
          <tr>
            <td>Contact number</td>
            <td>:- </td>
            <td>{{phone}}</td>
          </tr>
          <tr>
            <td>Remark</td>
            <td>:- </td>
            <td>{{remark}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>

</html>
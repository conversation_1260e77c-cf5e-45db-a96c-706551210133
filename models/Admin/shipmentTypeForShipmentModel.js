const {
	shipment_job,
	company,
	shipment_type_stage,
	shipment_type,
	shipment_job_assign_worker,
	customer,
	shipment_job_forced,
	shipment_job_signature,
	shipment_type_for_shipment,
	shipment_type_stage_for_shipment,
	qr_code,
	shipment_inventory_forced,
	shipment_inventory_job_scanned,
	staff,
	shipment_inventory,
	shipment_inventory_photo,
	shipment_room,
	shipment_inventory_exception,
	shipment_inventory_comments,
	shipment_exception,
	shipment_inventory_exception_note,
	shipment_inventory_location,
	shipment_location,
	tag_shipment,
	tag,
	sequelize,
} = require("../../database/schemas");
const {
	generateResponse,
} = require("../../assets/common");
const { where, fn, col, Op } = require("sequelize");
const staffModel = require("../Admin/staffModel");
const homeModel = require("../../models/APP/homeModel");
const { orderBy } = require("lodash");

exports.findAllShipmentOfCompany = async (body) => {
	const data = await shipment_job.findAndCountAll({
		where: {
			company_id: body.company_id,
		},
		order: [["created_at", "ASC"]],
	})
	return data
}

exports.findOldShipment = async (shipmentTypeId) => {
	const data = await shipment_type.findOne({
		where: {
			shipment_type_id: shipmentTypeId,
		}
	})
	return data
}

exports.upShipment = async (localShipmentTypeId, shipmentId) => {
	const data = await shipment_job.update(
		{
			local_shipment_type_id: localShipmentTypeId
		},
		{
			where: { shipment_job_id: shipmentId },
		}
	);
	return data
}

exports.upShipmentStageUpdate = async (shipmentStageId, shipmentId) => {
	const data = await shipment_job.update(
		{
			local_job_status: shipmentStageId
		},
		{
			where: { shipment_job_id: shipmentId },
		}
	);

	return data
}


exports.fetchShipmentTypeStagesForShipment = async (shipmentTypeId) => {

	return await shipment_type_stage.findAll({

		where: {

			shipment_type_id: shipmentTypeId,

		},

	});

}


exports.createShipmentTypeStagesForShipment = async (fetchShipmentTypeStagesForCompnay, createShipmentTypeForShipment) => {
	let newArrayData = [];
	for (let i = 0; i < fetchShipmentTypeStagesForCompnay.length; i++) {
		let container = {};

		container.name = fetchShipmentTypeStagesForCompnay[i].name;

		container.local_shipment_type_id = createShipmentTypeForShipment.local_shipment_type_id;

		container.shipment_job_id = createShipmentTypeForShipment.shipment_job_id;

		container.order_of_stages = fetchShipmentTypeStagesForCompnay[i].order_of_stages;

		container.status = fetchShipmentTypeStagesForCompnay[i].status;

		container.scan_require = fetchShipmentTypeStagesForCompnay[i].scan_require ? 1 : 0;

		container.remove_scan_require = fetchShipmentTypeStagesForCompnay[i].remove_scan_require ? 1 : 0;

		container.scan_into_storage = fetchShipmentTypeStagesForCompnay[i].scan_into_storage ? 1 : 0;

		container.allow_default_manual_label = fetchShipmentTypeStagesForCompnay[i].allow_default_manual_label ? 1 : 0;

		container.allow_default_qr_code_mode = fetchShipmentTypeStagesForCompnay[i].allow_default_qr_code_mode ? 1 : 0;

		container.add_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].add_items_to_inventory ? 1 : 0;

		container.assign_storage_units_to_items = fetchShipmentTypeStagesForCompnay[i].assign_storage_units_to_items ? 1 : 0;

		container.unassign_storage_units_from_items = fetchShipmentTypeStagesForCompnay[i].unassign_storage_units_from_items ? 1 : 0;

		container.remove_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].remove_items_to_inventory ? 1 : 0;

		container.enable_partial_complete_stage = fetchShipmentTypeStagesForCompnay[i].enable_partial_complete_stage ? 1 : 0;

		container.scan_out_of_storage = fetchShipmentTypeStagesForCompnay[i].scan_out_of_storage ? 1 : 0;

		container.is_add_item = fetchShipmentTypeStagesForCompnay[i].is_add_item ? 1 : 0;

		container.is_add_exceptions = fetchShipmentTypeStagesForCompnay[i].is_add_exceptions ? 1 : 0;

		container.supervisor_signature_require = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require ? 1 : 0;

		container.customer_signature_require = fetchShipmentTypeStagesForCompnay[i].customer_signature_require ? 1 : 0;

		container.why_supervisor_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_supervisor_signature_require_note;

		container.why_customer_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_customer_signature_require_note;

		container.supervisor_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_note_by_user;

		container.customer_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_note_by_user;

		container.supervisor_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_origin_to_all_pages;

		container.supervisor_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_destination_to_all_pages;

		container.customer_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_origin_to_all_pages;

		container.customer_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_destination_to_all_pages;

		container.ref_shipment_stage_id = fetchShipmentTypeStagesForCompnay[i].shipment_stage_id;

		newArrayData.push(container)

	}
	return await shipment_type_stage_for_shipment.bulkCreate(newArrayData);

}

exports.createNewShipmentType = async (body, shipmentId) => {
	const shipmentObj = {
		name: body.name,
		admin_id: body.admin_id,
		company_id: body.company_id,
		staff_id: body.staff_id,
		number_of_stages: body.number_of_stages,
		signature_require: body.signature_require,
		status: body.status,
		ref_shipment_type_id: body.shipment_type_id,
		shipment_job_id: shipmentId,
		is_deleted: body.is_deleted,
	}
	return await shipment_type_for_shipment.create(shipmentObj);
}



exports.viewShipmentStage = async (shipment_stage_id) => {
	return await shipment_type_stage_for_shipment.findOne({
		where: { local_shipment_stage_id: shipment_stage_id }
	});
};



exports.editShipmentStage = async (request) => {
	let shipmentStageObj = {
		name: request.name && request.name,
		order_of_stages: request.order_of_stages && request.order_of_stages,
		scan_require: request.scan_require && request.scan_require,
		remove_scan_require: request.remove_scan_require && request.remove_scan_require,
		scan_into_storage: request.scan_into_storage && request.scan_into_storage,
		scan_out_of_storage: request.scan_out_of_storage && request.scan_out_of_storage,
		is_add_item: request.is_add_item && request.is_add_item,
		is_add_exceptions: request.is_add_exceptions && request.is_add_exceptions,
		show_no_exceptions: request.show_no_exceptions && request.show_no_exceptions,
		PDF_time_require: request.PDF_time_require && request.PDF_time_require,
		allow_default_manual_label: request.allow_default_manual_label && request.allow_default_manual_label,
		allow_default_qr_code_mode: request.allow_default_qr_code_mode && request.allow_default_qr_code_mode,
		add_items_to_inventory: request.add_items_to_inventory && request.add_items_to_inventory,
		assign_storage_units_to_items: request.assign_storage_units_to_items && request.assign_storage_units_to_items,
		unassign_storage_units_from_items: request.unassign_storage_units_from_items && request.unassign_storage_units_from_items,
		remove_items_to_inventory: request.remove_items_to_inventory && request.remove_items_to_inventory,
		enable_partial_complete_stage: request.enable_partial_complete_stage && request.enable_partial_complete_stage,
		supervisor_signature_require:
			request.supervisor_signature_require &&
			request.supervisor_signature_require,
		customer_signature_require:
			request.customer_signature_require && request.customer_signature_require,
		why_supervisor_signature_require_note: request.why_supervisor_signature_require_note && request.why_supervisor_signature_require_note,
		why_customer_signature_require_note: request.why_customer_signature_require_note && request.why_customer_signature_require_note,
		supervisor_signature_require_at_origin_to_all_pages: request.supervisor_signature_require_at_origin_to_all_pages && request.supervisor_signature_require_at_origin_to_all_pages,
		supervisor_signature_require_at_destination_to_all_pages: request.supervisor_signature_require_at_destination_to_all_pages && request.supervisor_signature_require_at_destination_to_all_pages,
		customer_signature_require_at_origin_to_all_pages: request.customer_signature_require_at_origin_to_all_pages && request.customer_signature_require_at_origin_to_all_pages,
		customer_signature_require_at_destination_to_all_pages: request.customer_signature_require_at_destination_to_all_pages && request.customer_signature_require_at_destination_to_all_pages,
		updated_at: new Date(),
	};

	return await shipment_type_stage_for_shipment.update(
		shipmentStageObj,
		{ where: { local_shipment_stage_id: request.local_shipment_stage_id } }
	);
};


exports.getAllScannedInventory = async () => {
	const data = await shipment_inventory_job_scanned.findAndCountAll();
	return data
}

exports.getAllOverrideInventory = async () => {
	const data = await shipment_inventory_forced.findAndCountAll();
	return data
}

exports.getAllShipmentStages = async (shipmentId) => {
	const data = await shipment_type_stage_for_shipment.findAndCountAll({
		where: {
			shipment_job_id: shipmentId
		}
	});
	return data
}


exports.upSccanedShipmentStageUpdate = async (status_id, stageId) => {
	const data = await shipment_inventory_job_scanned.update(
		{
			local_current_stage_id: stageId
		},
		{
			where: { scan_status_id: status_id },
		}
	);

	return data
}


exports.upOverrideShipmentStageUpdate = async (status_id, stageId) => {
	const data = await shipment_inventory_forced.update(
		{
			local_current_stage_id: stageId
		},
		{
			where: { forced_status_id: status_id },
		}
	);

	return data
}

exports.getAllShipmentForcedList = async () => {
	const data = await shipment_job_forced.findAndCountAll();
	return data
}

exports.getAllShipmentSignatureList = async () => {
	const data = await shipment_job_signature.findAndCountAll();
	return data
}


exports.upShipmentForcedUpdatelocal = async (status_id, stageId) => {
	const data = await shipment_job_forced.update(
		{
			local_current_stage_id: stageId
		},
		{
			where: { forced_status_id: status_id },
		}
	);

	return data;
}


exports.upShipmentForcedUpdateAlter = async (status_id, stageId) => {
	const data = await shipment_job_forced.update(
		{
			local_altered_stage_id: stageId
		},
		{
			where: { forced_status_id: status_id },
		}
	);

	return data
}


exports.upShipmentSingatureUpdate = async (signature_id, stageId) => {
	const data = await shipment_job_signature.update(
		{
			local_stage: stageId
		},
		{
			where: { shipment_job_signature_id: signature_id },
		}
	);

	return data;
}

exports.fetchShipmentTypegForShipmentStagesForCompnay = async (shipment_job_id) => {
	return await shipment_type_stage_for_shipment.findAll({
		where: {
			shipment_job_id: shipment_job_id,
			status: "active",
		},
		order: [["order_of_stages", "ASC"]]

	});
}

exports.updateOtherShipmentDetails = async (request, shipmentStageObj) => {
	return await shipment_type_stage_for_shipment.update(
		shipmentStageObj,
		{ where: { local_shipment_stage_id: request.local_shipment_stage_id } }
	);
};

exports.shipmentTypeStageDetailLengthUpdate = async (stageId, number) => {
	return await shipment_type_for_shipment.update(
		{ number_of_stages: number },
		{ where: { local_shipment_type_id: stageId } }
	);
};

exports.updateStageOrderNumberShipmentTypeStage = async (stageId, number) => {
	return await shipment_type_stage_for_shipment.update(
		{ order_of_stages: number },
		{ where: { local_shipment_stage_id: stageId } }
	);
};

exports.deleteShipmentTypeStageModel = async (stageId) =>
	await shipment_type_stage_for_shipment.destroy({ where: { local_shipment_stage_id: stageId } })


"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'internal_note',
        {
          type: Sequelize.TEXT,
          defaultValue: "",
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "internal_note");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
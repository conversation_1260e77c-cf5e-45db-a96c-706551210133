const {
	qr_code,
	shipment_inventory: inventory,
	sequelize,
} = require("../../database/schemas");

exports.listQrCodeModel = async (request,jobId) => {
    const pageNo = request.pageNo ? request.pageNo : 1;
    const pageSize = request.pageSize ? request.pageSize : 10;
    const orderBy = request.orderBy ? request.orderBy : "created_at";
    const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
    const search = request.search ? request.search : "";
    const status = request.filter ? request.filter : "active";
    pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

    return qr_code.findAndCountAll({
        where: {
            job_id: jobId
        },
        attributes: [
            ...COMMON_QR_ATTRIBUTES,
            [
                sequelize.literal(
                    `(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
                ),
                "qr_image",
            ],
            [
                sequelize.literal(
                    `LPAD(label_number, 8, 0)`
                ),
                "label_number",
            ],
            [
                sequelize.literal(

                    `(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
                ),
                "item_id",
            ],
            [
                sequelize.literal(
                    `(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
                ),
                "isQrScanned",
            ],
            [
                sequelize.literal(
                    `(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
                ),
                "item_name",
            ],
        ],
        limit: parseInt(pageSize),
        offset: parseInt(start),
        order: [[orderBy, orderSequence]]

    })
}
"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "item_suggestion",
        "createdByUserRole",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("item_suggestion", "createdByUserRole");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};

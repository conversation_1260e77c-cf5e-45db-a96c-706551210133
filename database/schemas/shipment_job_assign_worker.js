"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_job_assign_worker = sequelize.define(
		"shipment_job_assign_worker",
		{
			shipment_job_worker_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_job_id: DataTypes.INTEGER,
			staff_id: DataTypes.INTEGER,
			role: {
				type: DataTypes.ENUM("worker", "supervisor"),
				defaultValue: "worker",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_job_assign_worker.associate = function (models) {
		// associations can be defined here

		shipment_job_assign_worker.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});

		shipment_job_assign_worker.belongsTo(models.staff, {
			as: "job_worker_detail",
			foreignKey: "staff_id",
		});
	};
	return shipment_job_assign_worker;
};

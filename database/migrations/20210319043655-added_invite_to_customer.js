'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn(
				'customers',
				'is_invited',
				{
					type: Sequelize.ENUM('PENDING', 'INVITED', 'VIEWED'),
					defaultValue: 'PENDING'
				}
			);
			await queryInterface.addColumn(
				'customers',
				'notes',
				{
					type: Sequelize.TEXT
				}
			);
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},
	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn(
				'customers',
				'is_invited',
			);
			await queryInterface.removeColumn(
				'customers',
				'notes',
			);
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	}
};
